<div>
    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
        wire:ignore
    >
        <div class="rounded-t-xl bg-gray-900 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white">لیست نظرات ارسال شده <span
                            class="rounded-md bg-red-500 px-2 text-sm"
                        >{{ \App\Models\SurveyResponse::count() }}</span></span>
                </div>
                <div class="flex items-center gap-3">
                    <div
                        class="flex items-center gap-4"
                        x-data="playerState()"
                        x-init="checkState()"
                    >

                        <div x-show="isPlaying">
                            <span
                                class="text-sm text-gray-500"
                                x-text="countdown"
                            ></span>
                            <span class="text-sm text-gray-500">ثانیه</span>
                        </div>
                        <div
                            class="text-sm font-bold text-gray-100"
                            x-show="isPlaying"
                        >
                            زمان بروزرسانی </div>
                        <button
                            class="flex items-center gap-2 text-white"
                            @click="togglePlay"
                        >
                            <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                                <svg
                                    class="size-6"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                    />
                                </svg>
                            </span>
                            <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                                <svg
                                    class="size-6"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                    />
                                </svg>
                            </span>
                        </button>

                        <button
                            class="flex items-center gap-2 p-2"
                            @click="fillter = !fillter"
                        >
                            <svg
                                class="h-6 w-6 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                                />
                            </svg>
                            <span class="text-base text-white">فیلتر</span>
                        </button>

                    </div>

                </div>
            </div>

        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto py-4' : 'h-0'"
        >

            <div class="my-3 grid grid-cols-1 gap-3 md:grid-cols-8">
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="order_code"
                    >شماره سفارش:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="order_code"
                        type="text"
                        wire:model="order_code"
                    >
                    @error('order_code')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="md:col-span-2">
                    <label
                        class="mb-2 block text-sm text-white"
                        for="fullname"
                    >نام گیرنده:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="fullname"
                        type="text"
                        wire:model="fullname"
                    >
                    @error('fullname')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="phone"
                    >شماره تماس گیرنده:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="text"
                        wire:model="phone"
                    >
                    @error('phone')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="last_status"
                    >وضعیت سفارش:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-1.5 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="last_status"
                        wire:model="last_status"
                    >
                        <option value="">--وضعیت--</option>
                        <option>در حال طراحی</option>
                        <option>منتظر انتخاب طرح</option>
                        <option>فایل برش</option>
                        <option style="font-weight:900;color:red">آماده به ساخت</option>
                        <option>در حال ساخت</option>
                        <option style="font-weight:900;color:blue">آماده به ارسال</option>
                        <option>درحال ارسال</option>
                        <option>منتظر تسویه مشتری</option>
                        <option>ارسال شد</option>
                        <option>کنسل</option>
                    </select>
                    @error('last_status')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="relative overflow-x-auto">
        @include('layouts.tools.loading')

        <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
            <thead class="bg-gray-200 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400">
                <tr>

                    <th
                        class="px-6 py-3 text-right"
                        scope="col"
                    >
                        <span class="text-sm">کدسفارش</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">نام گیرنده</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">تعداد کل سفارشات</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">شماره موبایل</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">نام کارشناس</span>
                    </th>

                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">تاریخ نظرسنجی</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">امتیاز به سفارش</span>
                    </th>
                    <th
                        class="px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm">تنظیمات</span>
                    </th>

                </tr>
            </thead>
            <tbody>
                @foreach ($reports as $item)
                    <tr
                        class="border-b bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600">

                        <td
                            class="border border-r-0 border-gray-200 px-2 py-3 text-center text-sm dark:border-gray-800 dark:bg-gray-800"
                            scope="col"
                        >
                            <div class="flex items-center justify-center gap-0">

                                <div>
                                    @if ($item->order->sex == 'طلا')
                                        <span
                                            class="rounded-md bg-gradient-to-r from-yellow-500 to-red-500 px-1.5 py-1 text-xs text-white transition-all hover:scale-125"
                                        >{{ $item->order->code }}</span>
                                    @else
                                        <span
                                            class="rounded-md bg-gradient-to-r from-gray-100 to-gray-300 px-1.5 py-1 text-xs text-gray-700 transition-all hover:scale-125"
                                        >{{ $item->order->code }}</span>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 px-6 py-3 py-4 text-center text-center text-sm dark:border-gray-700 dark:bg-gray-800">
                            <span
                                class="whitespace-nowrap text-sm font-bold text-gray-700">{{ $item->order->fullname }}</span>
                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 px-6 py-3 py-4 text-center text-center text-sm dark:border-gray-700 dark:bg-gray-800">
                            <span
                                class="whitespace-nowrap text-sm font-bold text-gray-700">{{ $item->order->orders->count() }}</span>
                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 px-6 py-3 py-4 text-center text-center text-sm dark:border-gray-700 dark:bg-gray-800">
                            <span
                                class="whitespace-nowrap text-sm font-bold text-gray-700">{{ $item->order->phone }}</span>
                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 py-3 text-center text-sm dark:border-gray-700 dark:bg-gray-800"
                            scope="col"
                        >
                            <a
                                class="mx-auto flex w-40 max-w-40 overflow-hidden rounded-lg bg-gray-200 px-2 py-1 transition-all hover:bg-gray-300 dark:bg-gray-800 dark:hover:bg-gray-800 max-md:whitespace-nowrap"
                                data-tooltip-target="tooltip-userRecipient-{{ $item->order->userRecipient->id }}"
                                href="{{ route('admin-dashboard-orders', ['user' => $item->user_id]) }}"
                                target="_blank"
                            >
                                <div class="flex w-40 items-center gap-1 text-right">
                                    <img
                                        class="h-8 w-8 shrink-0 rounded-full"
                                        src="{{ $item->order->user->avatar != null ? 'https://tdservice.ir/' . $item->order->user->avatar : '/assets/images/avatar.jpg' }}"
                                        alt="{{ $item->order->userRecipient->fullname }}"
                                    >
                                    <span
                                        class="txt-ellipsis block pr-2 text-xs text-gray-700 dark:text-gray-200">{{ $item->order->userRecipient->fullname }}</span>
                                </div>
                                {{-- <div id="tooltip-userRecipient-{{ $item->userRecipient->id }}"  role="tooltip" class="absolute z-10 flex items-center justify-center invisible px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 dark:font-normal transition-opacity duration-300 bg-white rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700">
                            {{ $item->userRecipient->fullname }}
                            <div class="tooltip-arrow" data-popper-arrow></div>
                        </div> --}}
                            </a>

                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 px-6 py-3 py-4 text-center text-center text-sm dark:border-gray-700 dark:bg-gray-800">
                            <span
                                class="whitespace-nowrap text-sm font-bold text-gray-700">{{ shamsiDate($item->created_at) }}</span>
                        </td>
                        <td
                            class="border border-r-0 border-gray-200 px-2 px-6 py-3 py-4 text-center text-center text-sm dark:border-gray-700 dark:bg-gray-800">

                            {{ $item->answerItems[count($item->answerItems) - 2]['answer_text'] ?? '' }}
                            <span class="px-4 text-gray-300">|</span>
                            {{ $item->answerItems[count($item->answerItems) - 1]['answer_text'] ?? '' }}
                        </td>
                        <td class="px-6 py-4 text-center">
                            <div class="flex items-center justify-center gap-3">
                                <a
                                    class="rounded-md bg-gray-800 px-3 py-2 text-sm text-white transition-all hover:bg-gray-700"
                                    href="{{ route('admin-dashboard-show-order', $item->survey_id) }}"
                                    target="_blank"
                                >
                                    <span class="flex items-center gap-2">
                                        <svg
                                            class="size-4"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                            />
                                        </svg>
                                        <span class="whitespace-nowrap text-xs">مشاهده سفارش</span>
                                    </span>
                                </a>
                                <button
                                    class="rounded-md bg-gray-100 px-3 py-2 text-sm text-gray-700 transition-all hover:bg-gray-200"
                                    type="button"
                                    @click="showSurvey = true; lock = true; $wire.dispatch('set-survey', { orderId : {{ $item->survey_id }} })"
                                >
                                    <span class="flex items-center gap-2">

                                        <span class="text-xs"> نظرسنجی</span>
                                    </span>
                                </button>
                            </div>
                        </td>
                    </tr>
                @endforeach

            </tbody>
        </table>
        <div class="my-3 overflow-hidden px-3 max-md:hidden">
            {{ $reports->links(data: ['dark' => false]) }}
        </div>
    </div>

</div>
