<div class="relative w-full rounded-xl bg-white px-3 md:p-5">
    <div class="text-right">
        <h3 class="text-lg font-bold">ایجاد محصول بدون اجرت</h3>
    </div>
    <form
        class="grid grid-cols-1 gap-3 md:mt-6 md:grid-cols-10"
        wire:submit="save"
    >
        <div class="mt-6 w-full md:col-span-6">
            <div class="grid grid-cols-1 gap-3 md:grid-cols-4">
                <div class="md:col-span-2">
                    <label
                        class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                        for="title"
                    >عنوان محصول:</label>
                    <input
                        class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="title"
                        type="text"
                        wire:model="form.title"
                        dir="rtl"
                    >
                    @error('form.title')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="md:col-span-2">
                    <label
                        class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                        for="fullname_customer"
                    >نام و نام خانوادگی فروشنده:</label>
                    <input
                        class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="fullname_customer"
                        type="text"
                        wire:model="form.fullname_customer"
                        dir="rtl"
                    >
                    @error('form.fullname_customer')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="grid grid-cols-2 gap-3 md:col-span-4 md:grid-cols-4">
                    <div>
                        <label
                            class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                            for="phone"
                        >شماره موبایل:</label>
                        <input
                            class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="phone"
                            type="tel"
                            wire:model="form.phone"
                            dir="ltr"
                        >
                        @error('form.phone')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div>
                        <label
                            class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                            for="national_number"
                        >شماره ملی:</label>
                        <input
                            class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="national_number"
                            type="tel"
                            wire:model="form.national_number"
                            dir="ltr"
                        >
                        @error('form.national_number')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div>
                        <label
                            class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                            for="work_rate"
                        >مظنه:</label>
                        <input
                            class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="work_rate"
                            type="tel"
                            wire:model="form.work_rate"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            dir="ltr"
                        >
                        @error('form.work_rate')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div>
                        <label
                            class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                            for="weight"
                        >وزن محصول:</label>
                        <input
                            class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="weight"
                            type="tel"
                            wire:model="form.weight"
                            dir="ltr"
                        >
                        @error('form.weight')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm font-bold text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                        for="payer_details"
                    >نام پرداخت کننده وجه:</label>
                    <input
                        class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="payer_details"
                        type="text"
                        wire:model="form.payer_details"
                        dir="rtl"
                    >
                    @error('form.payer_details')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="md:col-span-3">
                    <label
                        class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                        for="bank"
                    >شماره کارت و بانک:</label>
                    <input
                        class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="bank"
                        type="text"
                        wire:model="form.bank"
                        dir="rtl"
                    >
                    @error('form.bank')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="md:col-span-4">
                    <div>
                        <label
                            class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                            for="description"
                        >توضیحات بیشتر</label>

                        <textarea
                            class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                            id="description"
                            rows="3"
                            wire:model="form.description"
                        ></textarea>
                    </div>
                    @error('form.description')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

            </div>

        </div>
        <div class="md:col-span-4">
            <div class="h-96 min-h-96 border-gray-300 md:mr-6 md:border-r">
                <div class="border-gray-300 md:col-span-2 md:border-r md:p-6">
                    <div class="pb-6">
                        <h3 class="text-base font-bold">بارگزاری تصویر شناسایی و محصول</h3>
                        <p class="pt-2 text-sm text-gray-400">تصویر شناسنامه و یا کارت ملی و عکس محصول</p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:grid-cols-3">
                        @if ($files != [])
                            @foreach ($files as $key => $image)
                                @if (is_array($image) && isset($image['image']))
                                    <div
                                        class="dark:hover:bg-bray-800 relative ml-2 h-32 w-full cursor-pointer overflow-hidden rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600">
                                        <img
                                            class="rouded-lg h-full w-full"
                                            src="{{ $image['image'] }}"
                                        >
                                        <button
                                            class="absolute left-1 top-1 z-50 flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1 opacity-90 transition-all hover:bg-red-600 hover:text-white"
                                            data-ripple-dark="true"
                                            type="button"
                                            wire:click="removeImage({{ $key }})"
                                        >
                                            <svg
                                                class="h-4 w-4"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                @endif
                            @endforeach
                        @endif

                        <livewire:dashboard.admin.product.upload-image />

                    </div>
                </div>

            </div>
        </div>
        <div
            class="max-md:fixed max-md:bottom-0 max-md:left-0 max-md:z-[999] max-md:w-full max-md:bg-white max-md:p-2 max-md:pb-8 md:col-span-10">
            <div class="flex flex-row-reverse items-center md:p-5">
                <button
                    class="flex w-40 items-center justify-center rounded-lg bg-green-500 px-4 py-3 text-white transition-all hover:bg-green-600 max-md:w-full"
                    type="submit"
                >
                    <div
                        class="flex items-center gap-1"
                        wire:loading.remove
                        wire:target="save"
                    >
                        <span class="text-sm">ذخیره اطلاعات</span>
                    </div>
                    <span
                        wire:target="save"
                        wire:loading
                    >
                        <x-icon.loading />
                    </span>
                </button>
            </div>
        </div>
    </form>

</div>
