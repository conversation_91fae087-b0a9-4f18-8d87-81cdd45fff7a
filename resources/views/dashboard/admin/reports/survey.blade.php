@extends('layouts.dashboard')

@section('title', ' - لیست نظرسنجی مشتریان')

@section('content')
    {{-- <div class="bg-gray-700 w-full relative left-0 top-0 h-60"></div> --}}
    <main
        class="relative pb-20 md:px-8 md:pt-6"
        x-data="{ items: 1000, showSurvey: false }"
    >
        <div class="px-3 md:px-0">
            <div class="verflow-x-auto table-wrp block w-full rounded-xl bg-white shadow-xl dark:bg-gray-800">
                <livewire:dashboard.admin.reports.survey.index
                    wire:key="{{ Str::uuid() }}"
                    search="true"
                />
            </div>
        </div>
        <div
            class="fixed left-0 top-0 z-[1001] flex h-screen w-screen bg-gray-900/75 backdrop-blur-sm max-md:items-end md:h-full md:w-full md:items-center md:justify-center"
            x-transition:enter="transition md:ease-out duration-200"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition md:ease-in duration-200"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="showSurvey == true"
            x-cloak
        >
            <div
                class="md:fade-scale fadeOutNav relative w-full max-w-2xl overflow-y-auto bg-white dark:bg-gray-900 max-md:max-h-[90%] max-md:rounded-t-xl max-md:pb-10 md:inset-0 md:max-h-[100%] md:rounded-xl md:shadow-xl"
                x-show="showSurvey == true"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 md:scale-75"
                x-transition:enter-end="opacity-100 md:scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 md:scale-100"
                x-transition:leave-end="opacity-0 md:scale-75"
            >

                <div class="mb-3 mt-3 flex h-full justify-between p-5 pb-0 pt-0 dark:border-gray-800">
                    <div>

                    </div>
                    <button
                        class="mr-auto flex h-8 w-8 items-center justify-center rounded-lg bg-transparent p-3 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                        type="button"
                        @click="showSurvey = false, lock = false"
                    >
                        <svg
                            class="h-5 w-5 shrink-0"
                            aria-hidden="true"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <livewire:dashboard.admin.orders.survey.show-survey-modal />

            </div>
        </div>
    </main>
@stop
