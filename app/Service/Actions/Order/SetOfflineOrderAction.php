<?php

namespace App\Service\Actions\Order;

use App\Models\Checkout;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductDetail;
use App\Models\OrderDiscount;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\User;
use App\Service\CacheClear;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SetOfflineOrderAction
{
    public static function handle(array $data, int $userId, $mobile = null, $fullname = null, $baseUrlProduction = null): array
    {

        try {

            if (! self::ValidateInfo($data)) {
                return [
                    'status' => false,
                    'message' => 'تمام فیلدهای الزامی را پر کنید',
                ];
            }

            DB::beginTransaction();

            $checkouts = Checkout::where('user_id', auth()->id())->get();
            $data['totalFactor'] = auth()->user()->sumCheckout();
            // $data['discount'] = $discount;

            // dd($data['totalFactor']);

            if ($data['totalFactor'] < 1) {
                $data['complate'] = false;
                $data['getUserDeatail'] = false;
            }

            $code = self::generateOrderCode($data);
            $v = verta();

            $invoice = self::createInvoice($data, $userId, $checkouts);

            if (isset($invoice['status']) && ! $invoice['status']) {
                return [
                    'status' => false,
                    'message' => $invoice['message'],
                ];
            }

            $order = self::createOrder($code, $invoice, $data, $checkouts);

            self::createFactor($order, $data, $invoice, $checkouts);

            Checkout::where('user_id', auth()->user()->id)->delete();

            DB::commit();

            Artisan::call('optimize:clear');

            return [
                'status' => true,
                'orderId' => $order->id,
            ];

        } catch (\Exception $e) {

            DB::Rollback();

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];

        }
    }

    private static function ValidateInfo($data)
    {
        if ($data['fullname'] == null || $data['phone'] == null || $data['codepost'] == null || $data['address'] == null || $data['post'] == null || $data['category'] == null || $data['last_status'] == null || $data['userRefer'] == null) {
            return false;
        }

        return true;
    }

    private static function generateOrderCode($data)
    {
        $v = verta();
        $count = Sefaresh::where('month', $v->month)->where('year', $v->year)->count() + 1;

        $prefix = ($data['sex'] == 'طلا') ? 'G' : 'S';

        return "{$prefix}".substr($v->year, 3)."{$v->month}/{$count}";
    }

    private static function createInvoice($data, $userId, $checkouts)
    {
        // dd($data['discount']);
        $totalFactor = str_replace(',', '', $data['totalFactor']);
        $totalPost = str_replace(',', '', $data['MoneyPostOnline']);
        $discount = (float) str_replace(',', '', $data['discount']);
        $total = $totalFactor - $discount;
        // dd($checkouts->first()->details->gold18k);
        $invoice = Invoice::create([
            'user_id' => $userId,
            'discount' => $data['discount'],
            'total_factor' => $totalFactor,
            'total_payment' => $total + $totalPost,
            'fullname' => $data['fullname'],
            'phone' => $data['phone'],
            'zipcode' => $data['codepost'],
            'address' => $data['address'],
            'description' => $data['description'],
            'post' => $data['paymentOnlineChecked'],
            'money_post' => $totalPost,
            'gold18k' => (float) str_replace(',', '', optional($checkouts->first()->details)->gold18k
                             ?? optional($checkouts->get(1)?->details)->gold18k),

            'status' => 'success',
            'order_status' => 'person',
        ]);

        // $first = 0;
        foreach ($checkouts as $item) {

            // if ($first == 0) {

            //     $invoice = Invoice::whereId($invoice->id)->update([
            //         'gold18k' => $checkouts->first()->details->gold18k,
            //     ]);
            //     $first += 1;
            // }

            // if ($item->product->fixed_amount != null) {

            //     if ($item->product->getProductCount() == 0) {
            //         Artisan::call('optimize:clear');

            //         return [
            //             'status' => false,
            //             'message' => 'محصول '.$item->product->title_fa.' موجودی ندارد لطفا سبد خرید خود را مجدد چک کنید',
            //         ];
            //     }

            //     $product = Product::whereId($item->product->id)->first();
            //     $count = ((int) ($product->reserved_count ?? 0)) + (int) $item->count;
            //     $product->reserved_count = ((int) ($product->reserved_count ?? 0)) + (int) $item->count;
            //     $product->out_stock = $count == 0 ? 1 : null;
            //     $product->save();

            //     // dd($product->reserved_count = ((int) ($product->reserved_count ?? 0)) + (int) $item->count);

            //     // $item->product->reserved_count = $item->product->reserved_count + $item->count;
            //     // $item->product->save();

            //     // ProductCount::where('product_id', $item->product_id)->update([
            //     //     'count' => $item->product->ProductCount->count - 1,
            //     // ]);

            //     // Artisan::call('optimize:clear');
            // } else {

            if ($item->details->getProductDetailCount() == 0) {
                // Artisan::call('optimize:clear');

                return [
                    'status' => false,
                    'message' => 'محصول '.$item->product->title_fa.' موجودی ندارد لطفا سبد خرید خود را مجدد چک کنید',
                ];
            }

            $product = ProductDetail::whereId($item->details->id)->first();
            $product->reserved_count = 1;
            $product->out_stock = 1;
            $product->save();

            // dd($product->reserved_count += $item->count);
            // ProductCount::where('product_detail_id', $item->weight_id)->update([
            //     'count' => $product->ProductCount->count - 1,
            // ]);

            // CacheClear::clearKeys(['product_details_'.$item->product_id]);

            // }

            $product = InvoiceProduct::create([
                'invoice_id' => $invoice->id,
                'product_id' => $item->product_id,
                'product_detail_id' => $item->details->id,
                'title_fa' => $item->product->title_fa,
                'title_eng' => $item->product->title_eng,
                'category_id' => $item->product->category_id,
                'for_id' => $item->product->for_id,
                'description' => $item->product->description,
                'slug' => $item->product->slug,
                'count' => $item->count,
            ]);

            $amount = $item->product->fixed_amount != null ? str_replace(',', '', $item->product->fixed_amount) : str_replace(',', '', $item->product->detail->amount);

            InvoiceProductDetail::create([
                'invoice_id' => $invoice->id,
                'product_id' => $product->id,
                'product_detail_id' => $item->details->id,
                'eticket' => $item->details?->eticket,
                'weight' => $item->details?->weight,
                'cutie' => $item->details?->cutie,
                'profit' => $item->details?->profit,
                'color_id' => $item->details?->color_id,
                'construction_wages' => $item->details?->construction_wages,
                'branch_id' => $item->details?->branch_id,
                'count' => $item->count,
                'amount' => str_replace(',', '', $amount),
                // 'chain_size' => $item->details?->chain_size,
                'pluck' => $item->details?->pluck,
                'model' => $item->details?->model,
                'tax' => $item->details?->tax,
                'fixed_amount' => $item->product->fixed_amount,
                'chain_size' => str_replace(',', '', $item->details?->chain_size),
                'stone_price' => str_replace(',', '', $item->details?->stone_price),
            ]);

        }

        Artisan::call('optimize:clear');

        return $invoice;
    }

    private static function createFactor($order, $data, $invoice, $checkouts)
    {
        $total_payment = (float) str_replace(',', '', $invoice['total_payment']);
        $discount = (float) str_replace(',', '', $invoice['discount']);
        $total = $total_payment - $discount;

        $factorLatestId = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factor = FactorOrderEmpty::create([
            'user_id' => $data['userRefer'],
            'order_id' => $order->id,
            'fullname' => $data['fullname'],
            'phone' => $data['phone'],
            'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
            'factor_create_date' => todays(),
            'subscribe_code' => $data['code'],
            'factor_total' => $invoice['total_payment'],
            'factor_deposit' => 0,
            'factor_discount' => $discount,
            'total' => $total,
            'countWeight' => $data['totalWeight'],
            'post' => $data['post'],
            'gold18kup' => getGold18k(),
        ]);

        // شمارش تعداد آیتم‌های checkouts
        $count = $checkouts->count();

        // ایجاد ردیف‌ها برای هر آیتم در checkouts
        foreach ($checkouts as $item) {

            $amount = $item->product->fixed_amount != null ? str_replace(',', '', $item->product->fixed_amount) : str_replace(',', '', $item->product->detail->amount);
            // $weight = $item->product->fixed_amount != null ? null : $item->detail?->weight;
            $weight = $item->detail?->weight;
            // $construction_wages = $item->product->fixed_amount != null ? null : $item->detail?->construction_wages;
            $construction_wages = $item->detail?->construction_wages;
            $profit = $item->detail?->profit;
            // $profit = $item->product->fixed_amount != null ? null : $item->detail?->profit;
            // $eticket = $item->product->fixed_amount != item->product->eticket ? null : $item->product->detail?->eticket;

            FactorItemEmpty::create([
                'user_id' => $data['userRefer'],
                'order_id' => $order->id,
                'factor_id' => $factor->id,
                'subscribe_id' => $order->id,
                'code' => $order->code,
                'eticket' => $item->detail?->eticket,
                'title' => $item->product->title_fa,
                'gold18k' => $item->detail?->gold18k,
                'cutie' => '18',
                'post' => $item->product->fixed_amount != null ? 'yes' : '',
                'weight' => $weight,
                'construction_wages' => $construction_wages,
                'profit' => $profit,
                'total' => $item->product->fixed_amount != null ? formatMoney($item->product->fixed_amount) : null,
            ]);

            if ($item->detail->stone_price != null && $item->detail->stone_price != 0 && $item->detail->stone_price != '0') {
                FactorItemEmpty::create([
                    'user_id' => $data['userRefer'],
                    'order_id' => $order->id,
                    'factor_id' => $factor->id,
                    'subscribe_id' => $order->id,
                    'code' => '-',
                    'eticket' => '-',
                    'post' => 'yes',
                    'title' => 'هزینه سنگ',
                    'gold18k' => '0',
                    'cutie' => '0',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '0',
                    'total' => formatMoney($item->detail->stone_price),
                ]);
            }

        }

        if ($count < 5) {
            for ($i = 0; $i < 5 - $count; $i++) {
                FactorItemEmpty::create([
                    'user_id' => $data['userRefer'],
                    'order_id' => $order->id,
                    'factor_id' => $factor->id,
                    'subscribe_id' => $order->id,
                    'code' => null,
                    'eticket' => null,
                    'title' => null,
                    'gold18k' => null,
                    'cutie' => null,
                    'weight' => null,
                    'construction_wages' => null,
                    'profit' => null,
                    'total' => null,
                ]);
            }
        }

    }

    private static function createOrder($code, $invoice, $data, $checkouts)
    {

        $date = verta();
        $uniqueImages = $checkouts->map(function ($item) {
            return $item->product->gallery?->pluck('url')->first();
        })->filter()->unique()->values();

        $dataInvoice = [
            'product_count' => $checkouts->count(),
            // 'color' => getItemValue($checkouts->first()->detail?->color_id),
            'color' => 1,
            'product_image_1' => $uniqueImages->get(0) ? $uniqueImages->get(0) : null,
            'product_image_2' => $uniqueImages->get(1) ? $uniqueImages->get(1) : null,
            'product_image_3' => $uniqueImages->get(2) ? $uniqueImages->get(2) : null,
            'product_title' => $checkouts->count() == 1
                ? $checkouts->first()->product->title_fa
                : $checkouts->pluck('product.title_fa')->unique()->join(' + '),
        ];

        $invoiceData = $invoice?->productDetails?->first();

        $total_payment = (float) str_replace(',', '', $invoice['total_payment']);
        $discount = (float) str_replace(',', '', $invoice['discount']);
        $total = $total_payment - $discount;
        $order = Sefaresh::create(
            [
                'invoice_id' => $invoice->id,
                'code' => $code,
                'chat_id' => Str::uuid(),
                'user_id' => $data['userRefer'],
                'type' => $data['category'],
                'type_construction' => 'آماده',
                'model' => $dataInvoice['product_title'],
                'sex' => $data['sex'],
                'color' => $dataInvoice['color'],
                'font' => '-',
                'name_pluck' => '-',
                'post_type' => $data['post'],
                'tracking_code' => '-',
                'tips' => $data['description'],
                'total_amount' => $total_payment,
                'deposit1' => $total_payment,
                'deposit2' => '0',
                'remaining' => '0',
                'sefaresh_total' => $total_payment,
                'package_type' => '-',
                'package_amount' => '-',

                'fullname' => $data['fullname'],
                'phone' => $data['phone'],
                'address' => $data['address'],
                'codepost' => $data['codepost'],

                'chain' => '',
                'size' => $invoiceData['chain_size'] ?? null,
                'size_wrist' => '-',
                'size_ankle' => '-',

                'order_register_date' => todays(),
                'customer_date' => $data['customer_date'],
                'last_status' => $data['last_status'],
                'date_last_status' => todays(),

                'whatsapp' => 'حضوری',
                'phone_whatsapp' => '0',
                'product_code' => '0',

                'designer_id' => '',
                'manufacturer_id' => '',
                'dimensions' => '',

                'year' => $date->year,
                'month' => $date->month,
                'day' => $date->day,

                'gram' => $data['totalWeight'],
                'gold18k' => $invoice->gold18k,

                'image3' => $dataInvoice['product_image_1'],
                'image4' => $dataInvoice['product_image_2'],
                'image5' => $dataInvoice['product_image_3'],

            ]);

        // dd('test: ', $data['totalWeight']);

        $userRefer = User::where('id', $data['userRefer'])->first();
        $description = 'فروش حضوری به جمع سفارش: '.formatMoney($invoice['total_factor']).' و هزینه ارسال: '.formatMoney($invoice['post']).' توسط کاربر سیستم: '.auth()->user()->fullname.' به نام کارشناس: '.$userRefer?->fullname;
        if ($discount > 0) {
            $description = 'فروش حضوری به جمع سفارش: '.formatMoney($invoice['total_factor']).'که با تخفیف: '.formatMoney($discount).' که در نهایت کاربر مبلغ سبدخرید: '.formatMoney($total).' و هزینه ارسال: '.formatMoney($invoice['post']).' توسط کاربر سیستم: '.auth()->user()->fullname.' به نام کارشناس: '.$userRefer?->fullname;
        }

        $commission = calcCommission($data['totalWeight'], $invoice->gold18k);
        $profit = $data['totalWeight'] * $invoice->gold18k * 7 / 10;
        Financial::Create([
            'sefaresh_id' => $order->id,
            'user_id' => $data['userRefer'],
            'sefaresh_id' => $order->id,

            'chain' => '0',
            'package_amount' => '0',
            'amount_ready' => '0',
            'designing' => '0',
            'sender' => $invoice['post'],
            'total_amount' => $invoice->total_factor,
            'commission' => $commission,
            'packing' => '0',
            'profit' => $profit,
            'tipsGold' => $description,
        ]);

        StatusHistory::Create([
            'user_id' => $data['userRefer'],
            'sefaresh_id' => $order->id,
            'last_status' => '',
            'date_last_status' => '',
        ]);

        // OrderDiscount::Create([
        //     'order_id' => $order->id,
        //     'discount_code' => $data['discount_code'],
        //     'discount_percentage' => $data['discount_percentage'],

        //     'total_before_amount' => $data['total_before_amount'],
        //     'profit_before' => $data['profit_before'],

        //     'total_after_discount' => (float) str_replace(',', '', $invoice['total_payment']),
        //     'profit_after' => $data['profit_after'],
        // ]);

        return $order;
    }
}
