<?php

use App\Models\Optional;
use App\Models\ProductDetail;
use App\Models\ShortLinkPayment;
use <PERSON><PERSON><PERSON><PERSON>ser\Verta\Verta;
use hisorange\BrowserDetect\Parser as Browser;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

if (! function_exists('faTOen')) {
    function faTOen($string)
    {
        return strtr($string, ['۰' => '0', '۱' => '1', '۲' => '2', '۳' => '3', '۴' => '4', '۵' => '5', '۶' => '6', '۷' => '7', '۸' => '8', '۹' => '9', '٠' => '0', '١' => '1', '٢' => '2', '٣' => '3', '٤' => '4', '٥' => '5', '٦' => '6', '٧' => '7', '٨' => '8', '٩' => '9']);
    }
}
if (! function_exists('getDateTiem')) {
    function getDateTiem($time)
    {
        return new Verta($time);
    }
}

if (! function_exists('get_time')) {
    function get_time($time)
    {
        // return Verta::persianNumbers($time);
        return $time;
    }
}

if (! function_exists('get_ago')) {
    function get_ago($time)
    {
        return verta($time)->formatDifference();
    }
}

if (! function_exists('dateTimeToday')) {
    function dateTimeToday()
    {
        $dateToday = todays();

        return $dateToday.' '.date('H:i', time());
    }
}

if (! function_exists('todays')) {
    function todays($day = null)
    {

        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('toYear')) {
    function toYear()
    {
        $v = verta();
        $v->addYear();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('shamsiDate')) {
    function shamsiDate($date)
    {
        $v = new Verta($date);

        return $v->formatJalaliDatetime();
    }
}

if (! function_exists('shamsiDateLimit')) {
    function shamsiDateLimit($date, $year = null)
    {
        $v = new Verta($date);
        if ($year != null) {
            $v->addYear();
        }
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('TimeDateLimit')) {
    function TimeDateLimit($date)
    {
        $v = new Verta($date);

        return $v->hour.':'.$v->minute.':'.$v->second;
    }
}

if (! function_exists('shamsiDateDetails')) {
    function shamsiDateDetails()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $day.' '.convertIdtoTextMonth($month).' '.$v->year;
    }
}

if (! function_exists('shamsiDateArray')) {
    function shamsiDateArray()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return ['day' => $day, 'month' => convertIdtoTextMonth($month), 'year' => $v->year];
    }
}

if (! function_exists('CarbonDate')) {
    function CarbonDate($date)
    {
        return Verta::parse($date)->datetime();
        // return $v->jalaliToGregorian();
    }
}

if (! function_exists('setUserLog')) {
    function setUserLog($request, $phone = null, $description = null)
    {
        \App\Models\UserLog::create([
            'page' => url()->current(),
            'previous_page' => url()->previous(),
            'ip' => $request->ip(),
            'phone' => $phone,
            'browser' => Browser::browserName(),
            'platform' => Browser::platformName(),
            'device' => Browser::deviceType(),
            'description' => $description,
        ]);
    }
}

if (! function_exists('getGold18k')) {
    function getGold18k()
    {
        try {
            // نام کلید کش
            $cacheKey = 'gold_price_18k_with_settings';
            // مدت زمان کش (5 دقیقه = 300 ثانیه)
            $cacheDuration = 300;

            // بررسی کش
            $cachedPrice = Cache::get($cacheKey);

            if (! $cachedPrice) {
                $gold18kup = 0;

                // دریافت تنظیمات
                $settings = \App\Models\Setting::whereIn('type', [
                    'gold18k',
                    'gold18kup',
                    'gold18k_status',
                ])->pluck('body', 'type')->toArray();

                $gold18kup = $settings['gold18kup'] != null ? $settings['gold18kup'] : 0;
                $gold18kup = str_replace(',', '', $gold18kup);

                if ($settings['gold18k_status'] == 0) {
                    $gold18k = str_replace(',', '', $settings['gold18k']);
                    $result = formatMoney($gold18k + $gold18kup);

                    // ذخیره در کش
                    Cache::put($cacheKey, $result, $cacheDuration);

                    return $result;
                }

                // درخواست به API
                $apiUrl = 'https://www.tala.ir/ajax/price/talair';
                $response = Http::get($apiUrl);

                if (! $response->successful()) {
                    throw new Exception('Error in HTTP request. Status code: '.$response->status());
                }

                $jsonDecode = $response->json();
                $gold18k = $jsonDecode['gold']['gold_18k']['v'];

                $price = str_replace(',', '', $gold18k) * 10;
                $sum = faTOen($price) + $gold18kup;

                $cachedPrice = number_format($sum);

                // ذخیره در کش
                Cache::put($cacheKey, $cachedPrice, $cacheDuration);
            }

            return $cachedPrice;
        } catch (Exception $e) {
            return 0;
        }

    }
}

if (! function_exists('getGold18kOrigin')) {
    function getGold18kOrigin()
    {
        try {
            // نام کلید کش
            $cacheKey = 'gold_price_18k';
            // مدت زمان کش (5 دقیقه = 300 ثانیه)
            $cacheDuration = 300;

            // بررسی کش
            $goldPrice = Cache::get($cacheKey);

            if (! $goldPrice) {
                $apiUrl = 'https://www.tala.ir/ajax/price/talair';

                $response = Http::get($apiUrl);

                if (! $response->successful()) {
                    throw new Exception('Error in HTTP request. Status code: '.$response->status());
                }

                $jsonDecode = $response->json();
                $gold18k = $jsonDecode['gold']['gold_18k']['v'];

                $price = str_replace(',', '', $gold18k);
                $goldPrice = faTOen($price);
                $goldPrice = $goldPrice * 10;

                // ذخیره در کش
                Cache::put($cacheKey, $goldPrice, $cacheDuration);
            }

            return number_format($goldPrice);
        } catch (Exception $e) {
            return 0;
        }

    }
}

if (! function_exists('calculatePrice')) {
    function calculatePrice($productData, $gold18kOrigin, bool $formatmoney, $tax = 0.09)
    {

        $settings = \App\Models\Setting::whereIn('type', [
            'gold18k',
            'gold18kup',
            'gold18k_status',
        ])->pluck('body', 'type')->toArray();

        $gold18kOrigin = getGold18kOrigin();

        $gold18k = isset($settings['gold18k']) ? (float) str_replace(',', '', $settings['gold18k']) : 0;
        $gold18kup = isset($settings['gold18kup']) ? (float) str_replace(',', '', $settings['gold18kup']) : 0;

        if ($settings['gold18k_status'] == 1) {
            $gold18k = (float) str_replace(',', '', $gold18kOrigin);
        }

        $gold18k += $gold18kup;
        // dd($gold18k);

        $weight = (float) $productData['weight'];
        $constructionWagesPercent = (float) $productData['construction_wages'] / 100;
        $profitPercent = (float) $productData['profit'] / 100;
        $taxPercent = (float) $tax ?? $productData['tax'] / 100;
        $stone_price = isset($productData['stone_price']) ? (float) str_replace(',', '', $productData['stone_price']) : 0;

        $wage = $weight * $constructionWagesPercent;
        $profit = ($weight + $wage) * $profitPercent;
        $totalPriceBeforeTax = $weight + $wage + $profit;
        $totalPrice = $totalPriceBeforeTax * $gold18k;
        $tax = ($totalPrice - ($weight * $gold18k)) * $taxPercent;

        $finalPrice = $totalPrice + $tax + $stone_price;

        $finalPrice = floor($finalPrice / 10000) * 10000;

        // dd($gold18k, $weight, $constructionWagesPercent, $profitPercent, $taxPercent, $chain_size, $wage, $profit, $totalPriceBeforeTax, $totalPrice, $tax, $finalPrice);

        // if (isset($productData['id'])) {
        //     ProductDetail::whereId($productData['id'])?->update([
        //         'amount' => formatMoney($finalPrice),
        //     ]);

        //     return $finalPrice;
        //     // $cacheKey = 'product_amount_'.$productData['id'];

        //     // cache()->remember($cacheKey, now()->addMinutes(10), function () use ($productData, $finalPrice) {

        //     //     ProductDetail::whereId($productData['id'])?->update([
        //     //         'amount' => formatMoney($finalPrice),
        //     //     ]);

        //     //     return $finalPrice;
        //     // });
        // }

        $money = $formatmoney ? formatMoney($finalPrice) : $finalPrice;

        return ['money' => $money, 'gold18k' => formatMoney($gold18k)];

    }
}

if (! function_exists('getMoney')) {
    function getMoney($productData, $gold18kOrigin = null, bool $formatmoney = true, $tax = 0.09)
    {
        return calculatePrice($productData, $gold18kOrigin, $formatmoney, $tax);
    }
}

if (! function_exists('getMoneyInvoice')) {
    function getMoneyInvoice($productData, $gold18kOrigin = null, bool $formatmoney = true)
    {
        return calculatePrice($productData, $gold18kOrigin, $formatmoney);
    }
}

if (! function_exists('getProductDetailMoney')) {
    function getProductDetailMoney($productId, $detailId, $gold18kOrigin = null)
    {
        if (empty($productId)) {
            return 0;
        }

        // استفاده از کش برای ذخیره قیمت نهایی محصول به مدت 5 دقیقه
        $cacheKey = "product_price_{$productId}_{$detailId}";

        // بررسی وجود قیمت در کش
        $cachedPrice = Cache::get($cacheKey);
        if ($cachedPrice !== null) {
            return $cachedPrice; // اگر قیمت در کش موجود باشد، همان را برمی‌گرداند
        }

        // اگر قیمت در کش نباشد، محاسبه و ذخیره در کش
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($detailId, $gold18kOrigin, $cacheKey) {
            if ($gold18kOrigin == null) {
                $gold18kOrigin = getGold18kOrigin(); // دریافت قیمت طلا در صورت نیاز
            }

            $product = \App\Models\ProductDetail::whereId($detailId)->first();

            $settings = \App\Models\Setting::whereIn('type', [
                'gold18k',
                'gold18kup',
                'gold18k_status',
            ])->pluck('body', 'type')->toArray();

            $gold18k = 0;
            $gold18kup = $settings['gold18kup'] != null ? $settings['gold18kup'] : 0;
            $gold18kup = str_replace(',', '', $gold18kup);
            if ($settings['gold18k_status'] == 0) {
                $gold18k = str_replace(',', '', $settings['gold18k']);
            } else {
                $gold18k = str_replace(',', '', $gold18kOrigin);
            }

            $weight = $product->weight;
            $constructionWagesPercent = $product->construction_wages / 100;
            $profitPercent = $product->profit / 100;
            $cutie = $product->cutie;

            // محاسبه اجرت ساخت
            $wage = $weight * $constructionWagesPercent;

            // محاسبه سود
            $profit = ($weight + $wage) * $profitPercent;

            // محاسبه قیمت کل بدون مالیات
            $totalPriceBeforeTax = $weight + $wage + $profit;

            // محاسبه قیمت کل با ضرب در قیمت روز طلا
            $totalPrice = $totalPriceBeforeTax * $gold18k;

            // محاسبه مالیات
            $tax = ($totalPrice - ($weight * $gold18k)) * ($product->tax / 100);

            // محاسبه نهایی
            $finalPrice = $totalPrice + $tax + $gold18kup;

            // ذخیره قیمت نهایی در کش برای استفاده‌های بعدی
            Cache::put($cacheKey, formatMoney($finalPrice), now()->addMinutes(5));

            // به‌روزرسانی اطلاعات محصول در صورت نیاز
            // $product->amount = formatMoney($finalPrice);
            // $product->gold18k = $gold18k + $gold18kup;
            // $product->save();

            return formatMoney($finalPrice);
        });
    }
}

if (! function_exists('fixPhoneNumber')) {
    function fixPhoneNumber($phoneNumber)
    {
        $phoneNumber = str_replace('-', '', $phoneNumber);
        $phoneNumber = faTOen($phoneNumber);
        $phoneNumber = Str::replace(' ', '', $phoneNumber);
        $phoneNumber = Str::replace('+98', '0', $phoneNumber);

        return $phoneNumber;
    }
}

if (! function_exists('maskPhoneNumber')) {
    function maskPhoneNumber($phoneNumber)
    {
        // بررسی طول شماره موبایل
        if (strlen($phoneNumber) == 11) {
            // گرفتن پیش‌شماره و اعداد انتهایی شماره
            $prefix = substr($phoneNumber, 0, 4);
            $suffix = substr($phoneNumber, -3);

            // جایگزینی اعداد وسط با ستاره
            $maskedMiddle = '****';

            // ادغام پیش‌شماره، اعداد وسط و اعداد انتهایی با یکدیگر
            $maskedNumber = $prefix.$maskedMiddle.$suffix;

            return $maskedNumber;
        } else {
            // اگر طول شماره موبایل صحیح نباشد، پیام خطا را برگردان
            return '-';
        }
    }
}

if (! function_exists('getNameRoute')) {
    function getNameRoute($name)
    {
        switch ($name) {
            case 'jewelry':
                return ['text' => 'گردنبند', 'color' => 'bg-yellow-200 text-yellow-700'];

            case 'bracelet':
                return ['text' => 'دستبند', 'color' => 'bg-green-200 text-green-700'];

            case 'ring':
                return ['text' => 'انگشتر', 'color' => 'bg-green-200 text-green-700'];

            case 'spoon':
                return ['text' => 'ست', 'color' => 'bg-yellow-200 text-yellow-700'];

            case 'ankle_jewlery':
                return ['text' => 'پابند', 'color' => 'bg-yellow-200 text-yellow-700'];

            case 'jewelery':

                return ['text' => 'گوشواره', 'color' => 'bg-yellow-200 text-yellow-700'];
            case 'multi':

                return ['text' => 'چندسفارشه', 'color' => 'bg-yellow-200 text-yellow-700'];

        }
    }
}

if (! function_exists('status_last')) {
    function status_last($var)
    {
        if ($var == 'design') {
            return 'در حال طراحی';
        } elseif ($var == 'wait_design') {
            return 'منتظر انتخاب طرح';
        } elseif ($var == 'cut') {
            return 'فایل برش';
        } elseif ($var == 'ready_to_build') {
            return 'آماده به ساخت';
        } elseif ($var == 'wait_factory') {
            return 'در حال ساخت';
        } elseif ($var == 'ready') {
            return 'آماده به ارسال';
        } elseif ($var == 'ready-on') {
            return 'درحال ارسال';
        } elseif ($var == 'money') {
            return 'منتظر تسویه مشتری';
        } elseif ($var == 'send') {
            return 'ارسال شد';
        } elseif ($var == 'cancel') {
            return 'کنسل';
        } elseif ($var == 'created') {
            return 'ساخته شد';
        } else {
            return '-';
        }
    }
}

if (! function_exists('item_status')) {
    function item_status($status)
    {
        return match ($status) {
            'pending' => [
                'class' => 'bg-yellow-200 text-yellow-800',
                'text' => 'در انتظار',
            ],
            'reject' => [
                'class' => 'bg-red-600 text-white',
                'text' => 'رد شده',
            ],
            'success' => [
                'class' => 'bg-green-600 text-white',
                'text' => 'موفق',
            ],
            default => [
                'class' => 'bg-gray-300 text-gray-700',
                'text' => 'نامشخص',
            ],
        };
    }
}

if (! function_exists('color_status')) {
    function color_status($var)
    {
        if ($var == 'design') {
            return 'bg-yellow-300 text-yellow-800';
        } elseif ($var == 'wait_design') {
            return 'bg-red-500 text-white';
        } elseif ($var == 'cut') {
            return 'bg-blue-600 text-white';
        } elseif ($var == 'wait_factory' || $var == 'ready_to_build') {
            return 'bg-gray-100 text-gray-700';
        } elseif ($var == 'ready') {
            return 'bg-gray-900 text-white';
        } elseif ($var == 'ready-on') {
            return 'bg-blue-300 text-white';
        } elseif ($var == 'money') {
            return 'bg-gray-200 text-gray-800';
        } elseif ($var == 'send') {
            return 'bg-green-500 text-white';
        } elseif ($var == 'cancel') {
            return 'bg-red-500 text-white';
        } elseif ($var == 'created') {
            return 'bg-green-500 text-white';
        } else {
            return '-';
        }
    }
}

if (! function_exists('formatMoney')) {
    function formatMoney($money)
    {
        $money = str_replace([',', '،'], '', $money);
        $money = is_numeric($money) ? (int) $money : 0;

        return number_format($money, 0, '.', ',');
    }
}

if (! function_exists('getSrc')) {
    function getSrc($var)
    {

        if ($var === 'پست') {
            return '/assets/images/post.png';
        }

        if ($var == 'تیباکس') {
            return '/assets/images/Tipax.png';
        }

        if ($var === 'پست ویژه') {
            return '/assets/images/post.png';
        }
        if ($var == 'پیک') {
            return '/assets/images/payk.png';
        }
        if ($var == 'حضوری') {
            return '';
        }
        if ($var == 'حضوری الماس') {
            return '';
        }
        if ($var == 'حضوری همیلا') {
            return '';
        }

    }
}

if (! function_exists('getSendStatus')) {
    function getSendStatus($var)
    {

        if ($var === 'post') {
            return 'پست';
        }

        if ($var == 'tipax') {
            return 'تیپاکس';
        }
        if ($var == 'peyk') {
            return 'پیک ( تهران  و حومه - به عهده مشتری )';
        }
        if ($var == 'hamila') {
            return 'تحویل حضوری شعبه پونک ( پاساژ همیلا )';
        }
        if ($var == 'almas') {
            return 'تحویل حضوری شعبه جنت آباد ( پاساژ الماس )';
        }
    }
}

if (! function_exists('hashId')) {
    function hashId($id)
    {
        if (intval($id) == 0) {
            return 0;
        }
        $id = (((intval($id) * 53) + 147) - 9) + 12365;

        return $id;
    }
}

if (! function_exists('unHashId')) {
    function unHashId($id)
    {
        if (intval($id) == 0) {
            return 0;
        }
        $id = ((((intval($id) - 12365) + 9) - 147) / 53);

        return $id;
    }
}

if (! function_exists('saveFileFromTemporaryUrl')) {
    function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'Receipt')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (! $fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid().'.'.pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (! is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath.$fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }
}

if (! function_exists('saveFileFromTemporaryImage')) {
    function saveFileFromTemporaryImage($temporaryFile, $directory = 'Receipt')
    {
        try {
            if (! $temporaryFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                throw new Exception('Invalid file input.');
            }

            // ذخیره فایل در مسیر public
            $path = $temporaryFile->store("{$directory}");

            $path = 'storage/'.$path;

            // تبدیل مسیر به آدرس قابل دسترسی از طریق مرورگر
            return str_replace('public/', 'storage/', $path);
        } catch (Exception $e) {
            return null;
        }
    }
}

if (! function_exists('saveFileFromTemporaryUrl2')) {
    function saveFileFromTemporaryUrl2($temporaryUrl, $directory = 'Receipt')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (! $fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid().'.'.pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (! is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath.$fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }
}

if (! function_exists('saveFileFromTemporaryImage2')) {
    function saveFileFromTemporaryImage2($temporaryFile, $directory = 'Receipt')
    {
        try {
            if (! $temporaryFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                throw new Exception('Invalid file input.');
            }

            // ذخیره فایل در مسیر public
            $path = $temporaryFile->store("{$directory}");

            // تبدیل مسیر به آدرس قابل دسترسی از طریق مرورگر
            return 'storage/'.$path;
        } catch (Exception $e) {
            return null;
        }
    }
}

if (! function_exists('PhoneNumberFix')) {
    function PhoneNumberFix(string $phone)
    {
        // حذف فاصله‌ها و سایر کاراکترهای غیر ضروری
        $phone = preg_replace('/\D/', '', $phone); // حذف همه کاراکترهای غیر عددی

        // اگر شماره با 0 شروع نمی‌شود، به ابتدای آن 0 اضافه کن
        if (! str_starts_with($phone, '0')) {
            $phone = '0'.$phone;
        }

        // اصلاح پیشوندهایی مانند +98, 0098 و 98 به 09
        $phone = preg_replace('/^(?:\+98|0098)(\d{10})$/', '09$1', $phone);

        // اگر شماره از ابتدا با 98 شروع می‌شود، فقط شماره بدون پیشوند
        if (substr($phone, 0, 2) == '98' && strlen($phone) == 12) {
            $phone = '0'.substr($phone, 2);
        }

        return $phone;
    }
}

if (! function_exists('checkExitsProduct')) {
    function checkExitsProduct(int $productId) {}
}

if (! function_exists('getItemValue')) {
    function getItemValue(int $optionId)
    {
        return Optional::whereId($optionId)->pluck('description')->first();
    }
}

if (! function_exists('generateUniqueShortLink')) {
    function generateUniqueShortLink($length = 5)
    {
        do {
            $shortLink = Str::random(rand(3, $length));
        } while (ShortLinkPayment::where('short', $shortLink)->exists());

        return $shortLink;
    }

}

if (! function_exists('isValidIranianNationalCode')) {
    function isValidIranianNationalCode($code)
    {
        // حذف کاراکترهای غیرعددی
        $code = preg_replace('/[^0-9]/', '', $code);

        // بررسی طول کد ملی
        if (strlen($code) != 10) {
            return false;
        }

        // بررسی یکتا نبودن ارقام
        if (preg_match('/^(\d)\1{9}$/', $code)) {
            return false;
        }

        // محاسبه رقم کنترل
        $sum = 0;
        for ($i = 0; $i < 9; $i++) {
            $sum += (int) $code[$i] * (10 - $i);
        }
        $remainder = $sum % 11;
        $controlDigit = (int) $code[9];

        // بررسی صحت رقم کنترل
        if (($remainder < 2 && $controlDigit == $remainder) || ($remainder >= 2 && $controlDigit == (11 - $remainder))) {
            return true;
        }

        return false;
    }
}

if (! function_exists('checkServiceUrlImage')) {
    function checkServiceUrlImage($image) {}
}

function smsTrackingPost($code, $phone)
{

    try {
        $api_token = env('GHASEDAK_API');
        $template = 'Rahgiri';
        $lineNumber = '10008566';
        $type = 1;
        $receptor = faTOen($phone);
        $api = new \Ghasedak\GhasedakApi($api_token);
        $api->Verify($receptor, $type, $template, $code);
    } catch (\Ghasedak\Exceptions\ApiException $e) {
        // echo $e->errorMessage();
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    } catch (\Ghasedak\Exceptions\HttpException $e) {
        // echo $e->errorMessage();
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    }
}

function smsTrackingTipax($code, $phone)
{
    try {
        $api_token = env('GHASEDAK_API');
        $template = 'Tippax';
        $lineNumber = '10008566';
        $type = 1;
        $receptor = faTOen($phone);
        $api = new \Ghasedak\GhasedakApi($api_token);
        $api->Verify($receptor, $type, $template, $code);
    } catch (\Ghasedak\Exceptions\ApiException $e) {
        // echo $e->errorMessage();
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    } catch (\Ghasedak\Exceptions\HttpException $e) {
        // echo $e->errorMessage();
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    }
}

function smsTasviehMoshtari($code, $phone, $fullname, $status)
{
    try {
        $api_token = env('GHASEDAK_API');
        $template = 'TasviehMoshtari';
        $lineNumber = '10008566';
        $type = 1;
        $receptor = faTOen($phone);
        $api = new \Ghasedak\GhasedakApi($api_token);
        $api->Verify($receptor, $type, $template, $fullname, $code, $status);

    } catch (\Ghasedak\Exceptions\ApiException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    } catch (\Ghasedak\Exceptions\HttpException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    }
}

function smsErsalShodpPeyk($code, $phone)
{
    try {
        $api_token = env('GHASEDAK_API');
        $template = 'ErsalShodPayk';
        $lineNumber = '10008566';
        $type = 1;
        $receptor = faTOen($phone);
        $api = new \Ghasedak\GhasedakApi($api_token);
        $api->Verify($receptor, $type, $template, $code);

    } catch (\Ghasedak\Exceptions\ApiException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    } catch (\Ghasedak\Exceptions\HttpException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    }
}

function smsStatusMoshtari($code, $phone, $fullname, $status)
{
    try {
        $api_token = env('GHASEDAK_API');
        $template = 'StatusProduct';
        $lineNumber = '10008566';
        $type = 1;
        $receptor = faTOen($phone);
        $api = new \Ghasedak\GhasedakApi($api_token);
        $api->Verify($receptor, $type, $template, $fullname, $code, $status);

    } catch (\Ghasedak\Exceptions\ApiException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    } catch (\Ghasedak\Exceptions\HttpException $e) {
        return response()->json(['error' => ['خطا سرور', $e->errorMessage()]]);
    }
}

function calcCommission($weightSum, $gold18k)
{
    $totalCommission = 0;
    $weightSum = (int) str_replace(',', '', $weightSum);
    $gold18k = (float) str_replace(',', '', $gold18k);

    // dd($weightSum);
    if ($weightSum < 0.8) {
        $totalCommission = 300000;
    } elseif ($weightSum >= 0.8 && $weightSum <= 1.2) {
        $totalCommission = 400000;
    } else {
        $totalCommission = $gold18k * $weightSum * 0.01;
    }

    return $totalCommission;
}
