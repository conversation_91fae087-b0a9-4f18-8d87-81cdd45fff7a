<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;

class User extends Authenticatable
{
    protected $table = 'users';

    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fullname',
        'landline_number',
        'national_id',
        'education',
        'date_of_birth',
        'job',
        'username',
        'email',
        'mobile',
        'level',
        'status',
        'password', 'last_seen', 'whatsapp_id', 'telegram_id', 'personal_access_tokens',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function getTotal()
    {
        $v = verta();
        $total = '';
        if (auth()->user()->level == 'admin') {
            $total = \App\Models\Sefaresh::where('last_status', '!=', 'cancel')->get()
                ->map(function ($sefaresh) {

                    $sefaresh->total_amount = str_replace(',', '', $sefaresh->total_amount);

                    return $sefaresh;

                })->where('month', $v->month)->where('year', 'LIKE', $v->year)->sum('total_amount');

        } else {
            $total = \App\Models\Sefaresh::where('last_status', '!=', 'cancel')->get()
                ->map(function ($sefaresh) {

                    $sefaresh->total_amount = str_replace(',', '', $sefaresh->total_amount);

                    return $sefaresh;

                })->where('month', $v->month)->where('year', 'LIKE', $v->year)->where('user_id', '=', auth()->user()->id)->sum('total_amount');

            // $total = Sefaresh::where('user_id','=',auth()->user()->id)->sum('total_amount');
        }

        return number_format($total, 0);
    }

    public function orderCount()
    {
        $v = verta();
        $count = '';
        if (auth()->user()->level == 'admin') {
            $count = \App\Models\Sefaresh::where('month', $v->month)->where('year', 'LIKE', $v->year)->count();
        } else {
            $count = \App\Models\Sefaresh::where('user_id', auth()->user()->id)->where('month', $v->month)->where('year', 'LIKE', $v->year)->count();
        }

        return $count;
    }

    public function address(): HasMany
    {
        return $this->hasMany(UserAddress::class);
    }

    protected function isAdmin(): Attribute
    {
        return Attribute::make(
            get: fn (): bool => $this->level === 'admin'
        );
    }

    public function sumCheckout($moneyPostOnline = 0, $discountMoney = 0)
    {
        $moneyPostOnline = str_replace(',', '', $moneyPostOnline);
        $discountMoney = str_replace(',', '', $discountMoney);
        $checkouts = Checkout::where('user_id', auth()->id())->get();
        $totalCheckout = 0;

        if ($checkouts->count() == 0) {
            return 0;
        }
        if ($checkouts) {
            foreach ($checkouts as $item) {

                if ($item->product?->fixed_amount != null && $item->product?->fixed_amount != 0) {
                    $totalCheckout += str_replace(',', '', $item->product?->fixed_amount) * $item->count;
                } else {
                    $totalCheckout += str_replace(',', '', $item->detail?->amount) * $item->count;
                }

            }
        }

        return (float) $totalCheckout + (float) $moneyPostOnline + (float) $discountMoney;

    }

    public function sumDiscountCheckout($percentage = 0)
    {
        if ($percentage == 0) {
            return 0;
        }

        $checkouts = Checkout::where('user_id', auth()->id())->get();
        $totalCheckout = 0;

        if ($checkouts->count() == 0) {
            return 0;
        }
        if ($checkouts) {
            foreach ($checkouts as $item) {

                if ($item->product?->fixed_amount != null && $item->product?->fixed_amount != 0) {

                } else {

                    $gold18k = (float) str_replace(',', '', $item->detail->gold18k);

                    $weight = (float) $item->detail->weight;

                    $constructionWagesPercent = (float) $item->detail->construction_wages / 100;

                    $profitone = intval($item->detail->profit);

                    $finalProfitPercent = (float) ($profitone * $percentage) / 100;

                    $taxPercent = (float) $item->detail->tax / 100;
                    $stone_price = 0;

                    $wage = $weight * $constructionWagesPercent;
                    $profit = ($weight + $wage) * $finalProfitPercent / 100;

                    $totalPriceBeforeTax = $weight + $wage + $profit;

                    $totalPrice = $totalPriceBeforeTax * $gold18k;

                    $tax = ($totalPrice - ($weight * $gold18k)) * $taxPercent;

                    $finalPrice = $totalPrice + $tax + $stone_price / 10;

                    $finalPrice = floor($finalPrice / 10000) * 10000;

                    return $totalCheckout += $finalPrice;

                }

            }
        }

        return (float) $totalCheckout;

    }

    public function sumAfterDiscountCheckout()
    {
        return 0;
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        }

        return (bool) $role->intersect($this->roles)->count();
    }
}
