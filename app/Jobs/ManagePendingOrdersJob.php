<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\ShortLinkPayment;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Artisan;

class ManagePendingOrdersJob implements ShouldQueue
{
    use Queueable;

    public $baseUrlProduction;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL', 'https://shop.tidamode.ir/');
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $twoHoursAgo = Carbon::now()->subHours(2);

        // دریافت سفارش‌های pending که بیش از ۲ ساعت گذشته‌اند
        $pendingOrders = Invoice::where('status', 'pending')
            ->where('created_at', '<=', $twoHoursAgo)
            ->get();

        foreach ($pendingOrders as $order) {
            // به‌روزرسانی وضعیت سفارش
            $order->update(['status' => 'reject']);

            // دریافت جزئیات فاکتور و بروزرسانی موجودی محصولات
            $invoiceDetails = InvoiceProductDetail::where('invoice_id', $order->id)->get();

            foreach ($invoiceDetails as $item) {

                // if ($item?->product?->product?->fixed_amount != null) {
                //     Product::whereId($item->product->product->id)->update([
                //         'reserved_count' => $item?->product?->product?->reserved_count - $item->count,
                //     ]);
                // } else {

                ProductDetail::where('product_id', $item->product->product->id)->where('eticket', $item->eticket)->where('weight', $item->weight)->update([
                    'reserved_count' => null,
                ]);
                // }

            }

            $shortLink = ShortLinkPayment::select('id', 'factor_id', 'order_id', 'link')
                ->where('order_id', $item->id)
                ->first();

            if ($shortLink) {
                $link = $this->baseUrlProduction.'f/'.$shortLink->short;
                $data = [
                    'fullname' => $item->fullname,
                    'phone' => $item->phone,
                    'orderId' => hashId($item->id),
                    'link' => $link,
                ];
                SendPayamakRejectOrderJob::dispatch($data);
            }
        }

        // پاک‌سازی کش (فقط یک‌بار اجرا می‌شود)
        Artisan::call('optimize:clear');
    }
}
