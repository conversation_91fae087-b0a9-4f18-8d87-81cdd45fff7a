<?php

namespace App\Jobs;

use App\Models\ProductDetail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpdateAmountProductsJob implements ShouldQueue
{
    use Queueable;

    protected $signature = 'products:update-prices';

    protected $description = 'به‌روزرسانی قیمت محصولات';

    public function handle()
    {
        $chunkSize = 1000;

        $setting = \App\Models\Setting::whereIn('type', [
            'tax',
        ])->pluck('body', 'type')->toArray();

        // $tax = isset($setting['tax']) ? floatval($setting['tax']) / 100 : 0.09;

        ProductDetail::where('out_stock', null)->where('reserved_count', null)->latest()->select(['id', 'weight', 'construction_wages', 'profit', 'tax', 'stone_price'])->chunkById($chunkSize, function ($products) use ($setting) {
            foreach ($products as $item) {

                $tax = $item->tax != null ? (int) $item->tax / 100 : floatval($setting['tax']) / 100;
                $taxProduct = $item->tax != null ? $item->tax : floatval($setting['tax']);

                $result = getMoney($item->toArray(), null, true, $tax);

                $item->update([
                    'gold18k' => $result['gold18k'],
                    'amount' => $result['money'],
                    'tax' => $taxProduct,
                ]);

            }
        });

    }
}
