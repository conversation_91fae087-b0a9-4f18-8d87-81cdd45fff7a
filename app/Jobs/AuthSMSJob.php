<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AuthSMSJob implements ShouldQueue
{
    use Queueable;

    public $phone, $code, $username;
    /**
     * Create a new job instance.
     */
    public function __construct($phone, $code, $username)
    {
        $this->phone = $phone;
        $this->code = $code;
        $this->username = $username;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $api = new \Ghasedak\GhasedakApi(env('GHASEDAK_API'));
		$api->Verify($this->phone,'ForgetPassword', $this->username, $this->code);
    }
}
