<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Models\ShortLinkPayment;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class MessageUserInvoicePendingJob implements ShouldQueue
{
    use Queueable;

    public $baseUrlProduction;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL', 'https://shop.tidamode.ir/');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        Invoice::where('status', 'pending')
            ->where('created_at', '<', Carbon::now()->subMinutes(50))
            ->chunk(100, function ($pendingOrders) {
                foreach ($pendingOrders as $item) {

                    $shortLink = ShortLinkPayment::select('id', 'factor_id', 'order_id', 'link', 'short')
                        ->where('order_id', $item->id)
                        ->first();

                    if ($shortLink) {
                        $link = $this->baseUrlProduction.'f/'.$shortLink->short;
                        $data = [
                            'fullname' => $item->fullname,
                            'phone' => $item->phone,
                            'orderId' => hashId($item->id),
                            'link' => $link,
                        ];
                        SendPaymentReminderJob::dispatch($data);
                    }
                }
            });

    }
}
