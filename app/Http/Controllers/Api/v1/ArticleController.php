<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\ArticleRequest;
use App\Http\Requests\ArticlesRequest;
use App\Http\Requests\CommentRequest;
use App\Http\Resources\ArticleResource;
use App\Http\Resources\ArticlesResource;
use App\Http\Resources\CommentResource;
use App\Models\Article;
use DanHarrin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use DanH<PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ArticleController extends BaseController
{
    use WithRateLimiting;

    public function index(ArticlesRequest $request)
    {

        $categories = $request->query('categories');

        $query = Article::query();

        if ($categories) {
            $categories = explode(',', $categories);
            $query->whereIn('category_eng', $categories);
        }

        $articles = $query->paginate(3);

        $pagination = [
            'total_article' => $articles->total(),
            'current_page' => $articles->currentPage(),
            'last_page' => $articles->lastPage(),
            'next_page_url' => $articles->nextPageUrl(),
            'prev_page_url' => $articles->previousPageUrl(),
        ];

        return $this->sendResponse('لیست مقالات', [
            'articles' => ArticlesResource::collection($articles),
            'pagination' => $pagination,
        ]);
    }

    public function show(ArticleRequest $request)
    {

        $article = Article::where('slug', $request->slug)->first();

        if ($article) {
            return $this->sendResponse('جزئیات محصول', [
                new ArticleResource($article),
            ]);
        }

        return $this->sendError('پیدا نشدن محصول', ['message' => 'محصول مورد نظر پیدا نشد'], 422);
    }

    public function categories(Request $request)
    {

        $categories = [
            [
                'type' => 'menu',
                'title_eng' => 'gold',
                'title_fa' => 'طلا',
                'image' => 'https://tidamid.liara.run/_next/static/media/image%202.6b7639f4.svg',
            ],
            [
                'type' => 'menu',
                'title_eng' => 'accessory',
                'title_fa' => 'اکسسوری',
                'image' => 'https://tidamid.liara.run/_next/static/media/image%202.6b7639f4.svg',
            ],
        ];

        if ($request->type != null) {
            $filteredCategories = array_filter($categories, function ($category) {
                return $category['type'] === 'menu';
            });

            return $this->sendResponse('دسته بندی مقالات', $filteredCategories);
        }

        return $this->sendResponse('دسته بندی مقالات', $categories);

    }

    public function comments($articleId)
    {
        $article = Article::whereId(unHashId($articleId))->first();

        if ($article) {
            // if($article->comments){

            // }
            return $this->sendResponse('لیست کامنت ها', [
                'comments' => CommentResource::collection($article->comments),
                'count' => $article->comments->count(),
            ]);
        }

        return $this->sendError('پیدا نشدن محصول', ['message' => 'مقاله مورد نظر پیدا نشد'], 422);
    }

    public function comment_create(CommentRequest $request)
    {

        try {
            $this->rateLimit(1);

            $validator = Validator::make($request->all(), [
                'articleId' => 'required',
                'fullname' => 'required',
                'body' => 'required',
            ]);

            if ($validator->fails()) {
                return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید'], 422);
            }

            if ($request->phone != null) {
                $phone = faTOen($request->phone);
                $phone = trim(preg_replace('/\s+/', '', $phone));
                if (! preg_match('/^09[0-9]{9}$/', $phone)) {
                    return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت شماره موبایل اشتباه است'], 422);
                }
            }

            if ($request->email !== null) {
                if (! filter_var($request->email, FILTER_VALIDATE_EMAIL)) {
                    return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت پست الکترونیک اشتباه است'], 422);
                }
            }

            $article = Article::whereId(unHashId($request->articleId))->first();
            if (isset($article)) {
                $article->comments()->create([
                    'fullname' => $request->fullname,
                    'phone' => $request->phone,
                    'email' => $request->email,
                    'body' => $request->body,
                    'commentable_id' => $article->id,
                    'commentable_type' => 'App\Models\Article',
                ]);

                return $this->sendResponse('لیست کامنت ها', [
                    'message' => 'کامنت مقاله با موفقیت ثبت شد',
                ]);
            }

            return $this->sendError('پیدا نشدن محصول', ['message' => 'مقاله مورد نظر پیدا نشد'], 422);
        } catch (TooManyRequestsException $exception) {
            // $this->exError = "چند دقیقه ی دیگر درخواست بعدی خود را ثبت کنید";
            throw ValidationException::withMessages([
                'message' => "درخواست شما کمتر از 1 دقیقه پیش ارسال شده است، برای درخواست بعدی  {$exception->secondsUntilAvailable} ثانیه صبر کنید",
            ]);
        }

    }
}
