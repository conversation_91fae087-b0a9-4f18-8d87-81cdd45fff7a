<?php

namespace App\Http\Controllers;

use App\Jobs\SendPayamakSurveyJob;
use App\Models\ProductDetail;
use App\Models\Sefaresh;
use Carbon\Carbon;

class TestController extends Controller
{
    public $baseUrlProduction;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL', 'https://shop.tidamode.ir/');
    }

    public function index()
    {

        $chunkSize = 1000;

        $setting = \App\Models\Setting::whereIn('type', [
            'tax',
        ])->pluck('body', 'type')->toArray();

        // $tax = isset($setting['tax']) ? floatval($setting['tax']) / 100 : 0.09;

        ProductDetail::where('out_stock', null)->where('reserved_count', null)->latest()->select(['id', 'weight', 'construction_wages', 'profit', 'tax', 'stone_price'])->chunkById($chunkSize, function ($products) use ($setting) {
            foreach ($products as $item) {

                $tax = $item->tax != null ? (int) $item->tax / 100 : floatval($setting['tax']) / 100;
                $taxProduct = $item->tax != null ? $item->tax : floatval($setting['tax']);

                $result = getMoney($item->toArray(), null, true, $tax);

                $item->update([
                    'gold18k' => $result['gold18k'],
                    'amount' => $result['money'],
                    'tax' => intval($setting['tax']),
                ]);

            }
        });

        dd('finish products update price');

    }

    public function order_latest()
    {
        $from = Carbon::now()->subDays(3)->startOfDay();
        $to = Carbon::now()->endOfDay();

        $p = Sefaresh::whereBetween('created_at', [$from, $to])->whereNull('survey')->whereNull('survey_sms')->where('last_status', 'send')->first();

        if ($p) {
            $p->survey_sms = 'send';
            $p->save();

            $link = $this->baseUrlProduction.'survey/'.hashId($p->id);
            $data = [
                'fullname' => 'سیدعلی موسوی',
                'phone' => '09397192230',
                'orderId' => hashId($p->id),
                'link' => $link,
                'id' => $p->id,
            ];

            SendPayamakSurveyJob::dispatch($data);
            dd($data);
        }

        // $chunkSize = 1000;

        // // تعیین بازه زمانی از سه روز پیش تا امروز
        // $from = Carbon::now()->subDays(3)->startOfDay();
        // $to = Carbon::now()->endOfDay();

        // Sefaresh::whereBetween('created_at', [$from, $to])
        //     ->whereNull('survey')
        //     ->where('last_status', 'send')->chunkById($chunkSize, function ($products) {
        //         foreach ($products as $item) {

        //             $link = $this->baseUrlProduction.'survey/'.hashId($item->id);

        //             $data = [
        //                 'fullname' => $item->fullname,
        //                 'phone' => $item->phone,
        //                 'orderId' => hashId($item->id),
        //                 'link' => $link,
        //             ];

        //             dd($data);
        //             // SendPayamakSurveyJob::dispatch($data);
        //         }
        //     });
    }
}
