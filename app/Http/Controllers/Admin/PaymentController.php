<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\SendPayamakTransactionJob;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Sefaresh;
use App\Models\ShortLinkPayment;
use App\Models\StatusHistory;
use App\Models\User;
use App\Models\ZinbalTransaction;
use App\Service\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    protected $ZIBAL_MERCHANT_KEY;

    public $ZIBAL_CALLBACK_URL;

    public function __construct()
    {
        $this->ZIBAL_MERCHANT_KEY = env('ZIBAL_MERCHANT_KEY');
        $this->ZIBAL_CALLBACK_URL = env('ZIBAL_CALLBACK_URL');
    }

    public function payment(Request $req, $factorId)
    {

        $invoice = \App\Models\Invoice::whereId($factorId)->where('status', 'pending')->first();
        $amount = intval($invoice->total_payment);
        if ($amount < 1000) {
            return 'مبلغ کل فاکتور کمتر از 10000 ریال است، لطفا به مبلغ کل فاکتور دقت فرمایید';
        }
        $parameters = [
            'merchant' => $this->ZIBAL_MERCHANT_KEY, // required
            'callbackUrl' => $this->ZIBAL_CALLBACK_URL, // required
            'amount' => $amount, // required
            'orderId' => $factorId, // optional
            'mobile' => $invoice?->phone, // optional for mpg
        ];

        $response = $this->postToZibal('request', $parameters);

        if (isset($response->result) && $response->result == 100) {

            $startGateWayUrl = 'https://gateway.zibal.ir/start/'.$response->trackId;

            ZinbalTransaction::create([
                'factorId' => $factorId,
                'trackId' => $response->trackId,
                'amount' => $amount,
            ]);

            return redirect($startGateWayUrl);
        } else {
            echo 'خطا در ارتباط با بانک';
            echo '</br></br>';
            echo $response?->message;
            echo '</br></br>';
            echo 'مبلغ پرداخت: '.$amount;
        }
    }

    public function postToZibal($path, $parameters)
    {
        $url = 'https://gateway.zibal.ir/v1/'.$path;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        // dd($response);
        return json_decode($response);
    }

    public function verfication(Request $req)
    {

        if ($_GET['trackId']) {
            $transaction = ZinbalTransaction::where('trackId', $_GET['trackId'])->where('result', '100')->latest()->first();

            if (isset($transaction)) {
                return view('transaction.index', compact('transaction'));
            }
        }

        $zibal = ZinbalTransaction::where('trackId', $_GET['trackId'])->latest()->first();

        // dd($_GET['trackId'], $zibal->toArray());
        if ($_GET['success'] == 1 && isset($zibal)) {

            // start verfication
            $parameters = [
                'merchant' => $this->ZIBAL_MERCHANT_KEY, // required
                'trackId' => $_GET['trackId'], // required

            ];

            $response = $this->postToZibal('verify', $parameters);
            $transaction = ZinbalTransaction::where('trackId', $_GET['trackId'])->latest()->first();
            $invoice = Invoice::whereId($transaction->factorId)->latest()->first();

            // dd($transaction->toArray(), $response->result);
            if (isset($transaction) && $response->result == 100) {

                ZinbalTransaction::where('trackId', $_GET['trackId'])->update([
                    'paidAt' => $response->paidAt,
                    'cardNumber' => $response->cardNumber,
                    'status' => 1,
                    'amount' => $response->amount,
                    'refNumber' => $response->refNumber,
                    'description' => $response->description,
                    'result' => $response->result,
                    'message' => $this->resultCodes($response->result),

                ]);

                $dataImage = [
                    'User_Phone' => $invoice->phone,
                    'TrackId' => $_GET['trackId'],
                    'Amount' => formatMoney($invoice->total_payment).' Rial',
                    'CardNumber' => $response->cardNumber,
                    'PaidAt' => $response->paidAt,
                    'PaidAt_Shamsi' => shamsiDate($response->paidAt),
                ];

                $filename = $_GET['trackId'].'_'.$invoice->phone;
                $imageResive = $this->generateImageResevie($dataImage, $filename);

                $data = [
                    'user_id' => $invoice->user_id,
                    'post_type' => $invoice->post_type,
                    'model' => $invoice->productDetails->first()?->product->title_fa,
                    'total_factor' => $invoice->total_factor,
                    'total_payment' => $invoice->total_payment,
                    'money_post' => $invoice->money_post,
                    'fullname' => $invoice->fullname,
                    'phone' => $invoice->phone,
                    'zipcode' => $invoice?->zipcode,
                    'address' => $invoice?->address,
                    'weight' => $invoice->productDetails->sum('weight'),
                    'gold18k' => $invoice->gold18k,
                    'tips' => $invoice->description,
                    'invoice_id' => $invoice->id,
                    'invoiceId' => $_GET['trackId'],
                    'image1' => $imageResive,
                ];

                $this->insertOrderAfterSuccessTransaction($data);

                $transaction = ZinbalTransaction::where('trackId', $_GET['trackId'])->latest()->first();

                return view('transaction.index', compact('transaction'));

            } else {
                return view('transaction.index');
            }

        } else {
            return view('transaction.index');
        }
    }

    private function insertOrderAfterSuccessTransaction($data)
    {

        // dd($data);
        try {

            DB::beginTransaction();

            Invoice::whereId($data['invoice_id'])->update([
                'status' => 'success',
            ]);

            $v = verta();
            $code = '';

            $c = Sefaresh::where('month', '=', $v->month)->where('year', '=', $v->year)->count();
            if (isset($c)) {
                $c += 1;
                $code = 'G'.substr($v->year, 3).$v->month.'/'.$c;
            }

            $totalPayment = (float) str_replace(',', '', $data['total_payment']);
            $totalPayment = (float) str_replace(',', '', $data['total_payment']) + (float) str_replace(',', '', $data['money_post']);

            $order = Sefaresh::updateOrCreate(
                [
                    'code' => $code,
                ],
                [
                    'invoice_id' => $data['invoice_id'],
                    'chat_id' => Str::uuid(),
                    'user_id' => $data['user_id'],
                    'type' => 'multi',
                    'type_construction' => 'آماده',
                    'model' => $data['model'],
                    'sex' => 'طلا',
                    'color' => '',
                    'font' => '',
                    'name_pluck' => '',
                    'post_type' => $data['post_type'],
                    'tracking_code' => '',
                    'tips' => '',
                    'total_amount' => $data['total_payment'],
                    'deposit1' => $data['total_payment'],
                    'deposit2' => '',
                    'remaining' => 0,
                    'sefaresh_total' => $data['total_payment'],
                    'package_type' => '',
                    'package_amount' => '',

                    'fullname' => $data['fullname'],
                    'phone' => $data['phone'],
                    'address' => $data['address'],
                    'codepost' => $data['zipcode'],

                    'chain' => '',
                    'size' => '',
                    'size_wrist' => '',
                    'size_ankle' => '',

                    'order_register_date' => todays(),
                    'customer_date' => '-',
                    'last_status' => 'ready',
                    'date_last_status' => todays(),

                    'whatsapp' => 'سامانه',
                    'phone_whatsapp' => '0',
                    'product_code' => '0',

                    'designer_id' => '',
                    'manufacturer_id' => '',
                    'dimensions' => '',

                    'image1' => $data['image1'],

                    'year' => $v->year,
                    'month' => $v->month,
                    'day' => $v->day,

                    'gram' => $data['weight'],
                    'gold18k' => $data['gold18k'],

                ]);

            $transaction = ZinbalTransaction::where('TrackId', $data['invoiceId'])->update([
                'orderId' => $order->id,
            ]);

            $userRefer = User::where('id', $data['user_id'])->first();

            // dd($data['money_post'], $data['total_factor'] + $data['money_post'], $data['total_payment']);
            Financial::create(
                [
                    'sefaresh_id' => $order->id,
                    'user_id' => $data['user_id'],
                    'sefaresh_id' => $order->id,
                    'chain' => '0',
                    'package_amount' => '0',
                    'amount_ready' => '0',
                    'designing' => '0',
                    'sender' => formatMoney($data['money_post']),
                    'total_amount' => $data['total_payment'],
                    'commission' => calcCommission($data['weight'], $data['gold18k']),
                    'packing' => '0',
                    'profit' => '0',
                    'tipsGold' => 'فروش حضوری به جمع سفارش: '.formatMoney($data['total_factor']).' و هزینه ارسال: '.formatMoney($data['money_post']).' به نام کارشناس: '.$userRefer?->fullname,
                ]
            );

            $historys = StatusHistory::Create([
                'user_id' => $data['user_id'],
                'sefaresh_id' => $order->id,
                'last_status' => 'ready',
                'date_last_status' => now(),
            ]);

            $invoices = Invoice::whereId($data['invoice_id'])->get();

            $factorLatestId = FactorOrderEmpty::where('order_id', $order->id)->latest()->first();
            $factor = FactorOrderEmpty::create([
                'user_id' => $order->user_id,
                'order_id' => $order->id,
                'fullname' => $order->fullname,
                'phone' => $order->phone,
                'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
                'factor_create_date' => todays(),
                // 'subscribe_code' => $data['code'],
                'factor_total' => $data['total_payment'],
                'factor_deposit' => $data['total_payment'],
                'factor_discount' => 0,
                'total' => $data['total_payment'],
                'countWeight' => $data['weight'],
                // 'post' => $data['post'],
                'gold18kup' => $order->gold18k,
                // 'factor_discount' => $invoice->discount,
            ]);

            $validCategories = ['jewelry', 'bracelet', 'ring', 'spoon', 'ankle_jewlery', 'jewelery', 'multi'];
            foreach ($invoices as $invoice) {

                $factor->factor_discount = $invoice->discount;
                $factor->save();

                foreach ($invoice->productDetails as $detail) {

                    // if ($detail?->fixed_amount != null) {

                    //     $product = Product::whereId($detail->product_id)->first();
                    //     $count = ((int) ($product->reserved_count ?? 0)) + (int) $detail->count;
                    //     // $product->reserved_count = ((int) ($product->reserved_count ?? 0)) - (int) $detail->count;
                    //     $product->out_stock = $count == 0 ? 1 : null;
                    //     $product->save();

                    // } else {
                    $product = ProductDetail::whereId($detail->product_detail_id)->first();
                    // $product->reserved_count = 1;
                    $product->out_stock = 1;
                    $product->save();
                    // }

                    $category = $detail?->product?->category?->category;
                    $type = in_array($category, $validCategories) ? $category : 'multi';

                    Sefaresh::whereId($order->id)->update([
                        'type' => $type,
                        'color' => $detail?->color?->description,
                        'order_type' => $detail?->product?->category?->title_eng,
                        'image3' => $invoice->image,
                        'size' => $detail->chain_size,
                    ]);
                    $amount = $detail->fixed_amount != null
                        ? str_replace(',', '', $detail->fixed_amount)
                        : str_replace(',', '', $detail->amount);

                    // $weight = $detail->product->fixed_amount != null ? null : $detail->weight;
                    $weight = $detail->weight;
                    // $construction_wages = $detail->product->fixed_amount != null ? null : $detail->construction_wages;
                    $construction_wages = $detail->construction_wages;
                    $profit = $detail->profit;
                    // $profit = $detail->product->fixed_amount != null ? null : $detail->profit;
                    // $eticket = $detail->product->fixed_amount != null ? null : $detail->eticket;
                    $eticket = $detail->eticket;

                    FactorItemEmpty::create([
                        'user_id' => $order->user_id,
                        'code' => $order->code,
                        'order_id' => $order->id,
                        'factor_id' => $factor->id,
                        'eticket' => $eticket,
                        'title' => $detail->product->title_fa,
                        'gold18k' => $order->gold18k,
                        'cutie' => '18',
                        'weight' => $weight,
                        'post' => $detail->fixed_amount != null ? 'yes' : '',
                        'construction_wages' => $construction_wages,
                        'profit' => $profit,
                        'total' => $detail->fixed_amount != null ? formatMoney($detail->fixed_amount) : null,
                        // 'factor_discount' => $invoice->discount,
                    ]);

                }
            }

            $postMoney = isset($data['money_post']) ? str_replace(',', '', $data['money_post']) : null;
            if ($postMoney > 0 && $postMoney != null) {
                FactorItemEmpty::create([
                    'user_id' => $order->user_id,
                    'code' => '-',
                    'eticket' => '-',
                    'cutie' => '0',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '0',
                    'order_id' => $order->id,
                    'title' => 'هزینه ارسال پست',
                    'factor_id' => $factor->id,
                    'gold18k' => $order->gold18k,
                    'post' => 'yes',
                    'sender' => formatMoney($data['money_post']),
                    'total' => formatMoney($data['money_post']),
                ]);
            }

            // اگر تعداد آیتم‌های checkouts کمتر از ۵ بود، ردیف‌های خالی اضافه کن
            if ($invoices->count() < 5) {
                for ($i = 0; $i < 5 - $invoices->count(); $i++) {
                    FactorItemEmpty::create([
                        'user_id' => $order->user_id,
                        'order_id' => $order->id,
                        'factor_id' => $factor->id,
                        // 'subscribe_id' => $order->id,
                        'code' => null,
                        'eticket' => null,
                        'title' => null,
                        'gold18k' => null,
                        'cutie' => null,
                        'weight' => null,
                        'construction_wages' => null,
                        'profit' => null,
                        'total' => null,
                    ]);
                }
            }

            DB::commit();

        } catch (\Exception $e) {

            DB::Rollback();

            echo $e->getMessage();

        }

    }

    public function generateImageResevie($data, $filename)
    {

        try {
            // آدرس عکس موردنظر
            $imagePath = public_path('/tidamode_logo_2025.png');

            // فراخوانی Helper
            $imageUrl = ImageHelper::generateImage($data, $filename.'.png', $imagePath);

            // نمایش لینک
            return $imageUrl;
        } catch (\Exception $e) {
            \Log::error('Error in ImageHelper: '.$e->getMessage());

            return null;
        }

    }

    public function orderGenerate(Request $req)
    {
        // dd($req->all());
        $req->merge([
            'amount' => (float) str_replace(',', '', $req->amount),
        ]);

        $validated = $req->validate([
            'fullname' => 'required|min:3|max:1000',
            'description' => 'required|min:3|max:10000',
            'phone' => 'required|numeric',
            'amount' => 'required|numeric|min:100000',
            'send' => 'required',
            'zipcode' => 'required|numeric',
        ], [
            'amount.min' => 'مبلغ وارد شده باید حداقل 100,000 تومان باشد.',
            'amount.numeric' => 'مبلغ باید یک عدد باشد.',
        ]);

        $send = 0;
        if ($req->get('send') == 'post') {
            $send = 1500000;
        }

        $productView = DB::table('product_details_view')->where('product_id', unHashId($req->productId))->where('product_detail_id', unHashId($req->productDetailId))->first();
        $productDetail = ProductDetail::where('product_id', unHashId($req->productId))->where('id', unHashId($req->productDetailId))->first();
        $amount = $productDetail->product->fixed_amount != null ? $productDetail->product->fixed_amount : $productDetail->amount;

        // return [$productDetail->product->fixed_amount, $productDetail->amount];
        // return $productDetail->toArray();
        if (isset($productDetail) && $productDetail != null) {
            $invoice = Invoice::create([
                'user_id' => unHashId($req?->userId) ?? 1,
                'total_factor' => (float) str_replace(',', '', $amount),
                // 'total_payment' => str_replace(',', '', $req->amount) * 10,
                'total_payment' => (float) str_replace(',', '', $amount) + (float) $send,
                'fullname' => $req->fullname,
                'phone' => $req->phone,
                'description' => $req->description,
                'address' => $req->description,
                'zipcode' => $req->zipcode,
                'post' => true,
                'money_post' => (float) $send,
                'gold18k' => $productDetail?->gold18k ?? getGold18k(),
            ]);

            // if ($productView->fixed_amount != null) {

            //     Product::whereId($productDetail->product->id)->update([
            //         'reserved_count' => 1 + (int) $productDetail->product->reserved_count,
            //     ]);

            // } else {

            ProductDetail::where('product_id', $productDetail->product->id)->whereId($productDetail->id)->update([
                'reserved_count' => 1 + (int) $productDetail->reserved_count,
            ]);

            // }

            $invoiceProduct = InvoiceProduct::create([
                'invoice_id' => $invoice->id,
                'product_id' => $productView->product_id,
                'title_fa' => $productView->product_title_fa,
                'title_eng' => $productView->product_title_eng,
                'category_id' => $productView->category_id,
                'for_id' => $productView->for_id,
                'description' => $productDetail->description,
                'slug' => $productDetail->product->slug,
                'count' => 1,

            ]);

            InvoiceProductDetail::create([
                'invoice_id' => $invoice->id,
                'product_id' => $invoiceProduct->id,
                'product_detail_id' => $productDetail->id,
                'eticket' => $productDetail->eticket,
                'weight' => $productDetail->weight,
                'cutie' => $productDetail->cutie,
                'profit' => $productDetail->profit,
                'color_id' => $productDetail->color_id,
                'construction_wages' => $productDetail->construction_wages,
                'branch_id' => $productDetail->branch_id,
                'count' => 1,
                'amount' => str_replace(',', '', $amount),
                'pluck' => $productDetail->pluck,
                'model' => $productDetail->model,
                'tax' => $productDetail->tax,
                'fixed_amount' => $productView->fixed_amount,
                'chain_size' => str_replace(',', '', $productDetail->chain_size),
                'stone_price' => str_replace(',', '', $productDetail->stone_price),
            ]);

            $baseUrlProduction = env('APP_TIDAMODE_SHOP_URL');
            $url = $baseUrlProduction.'factor/'.hashId($invoice->id);

            $sortLink = generateUniqueShortLink(5);
            ShortLinkPayment::create([
                'user_id' => unHashId($req?->userId) ?? 1,
                'order_id' => $invoice->id,
                'link' => $url,
                'short' => $sortLink,
                'fullname' => $req->fullname,
                'phone' => $req->phone,
                'status' => 1,
            ]);

            $link = $baseUrlProduction.'f/'.$sortLink;
            $data['shortLink'] = $link;
            $time = date('H:i', time());

            // $user = User::whereId(unHashId($req->userId))->first();

            // if (app()->environment('production')) {
            SendPayamakTransactionJob::dispatch($req->phone, $req->fullname, $link);

            // if(){
            //     SendPayamakTransactionAdminJob::dispatch(
            //         $userMobile,
            //         $userFullname,
            //         todays(),
            //         $time,
            //         $data['fullname'],
            //         $data['phone'],
            //         $link
            //     );
            // }

            // }

            return redirect()->route('factor-payment', parameters: $invoice->id);
        }

        return 'اطلاعات ارسال شده اشتباه می باشد';
    }

    public function resultCodes($code)
    {
        switch ($code) {
            case 100:
                return 'با موفقیت تایید شد';

            case 102:
                return 'merchant یافت نشد';

            case 103:
                return 'merchant غیرفعال';

            case 104:
                return 'merchant نامعتبر';

            case 201:
                return 'قبلا تایید شده';

            case 105:
                return 'amount بایستی بزرگتر از 1,000 ریال باشد';

            case 106:
                return 'callbackUrl نامعتبر می‌باشد. (شروع با http و یا https)';

            case 113:
                return 'amount مبلغ تراکنش از سقف میزان تراکنش بیشتر است.';

            case 201:
                return 'قبلا تایید شده';

            case 202:
                return 'سفارش پرداخت نشده یا ناموفق بوده است';

            case 203:
                return 'trackId نامعتبر می‌باشد';

            default:
                return 'وضعیت مشخص شده معتبر نیست';
        }
    }

    public function statusCodes($code)
    {
        switch ($code) {
            case -1:
                return 'در انتظار پردخت';

            case -2:
                return 'خطای داخلی';

            case 1:
                return 'پرداخت شده - تاییدشده';

            case 2:
                return 'پرداخت شده - تاییدنشده';

            case 3:
                return 'لغوشده توسط کاربر';

            case 4:
                return '‌شماره کارت نامعتبر می‌باشد';

            case 5:
                return '‌موجودی حساب کافی نمی‌باشد';

            case 6:
                return 'رمز واردشده اشتباه می‌باشد';

            case 7:
                return '‌تعداد درخواست‌ها بیش از حد مجاز می‌باشد';

            case 8:
                return '‌تعداد پرداخت اینترنتی روزانه بیش از حد مجاز می‌باشد';

            case 9:
                return 'مبلغ پرداخت اینترنتی روزانه بیش از حد مجاز می‌باشد';

            case 10:
                return '‌صادرکننده‌ی کارت نامعتبر می‌باشد';

            case 11:
                return 'خطای سوییچ';

            case 12:
                return 'کارت قابل دسترسی نمی‌باشد';

            default:
                return 'وضعیت مشخص شده معتبر نیست';
        }
    }
}
