<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductWithoutFee;

class ProductController extends Controller
{
    public function index()
    {
        return view('dashboard.admin.product.index');
    }

    public function without_fee_list()
    {
        return view('dashboard.admin.product.without-fee.index');
    }

    public function create()
    {
        return view('dashboard.admin.product.create');
    }

    public function show($productId)
    {
        $product = Product::whereId(unHashId($productId))->firstorfail();

        return view('dashboard.admin.product.edit', compact('product'));
    }

    public function withoutـfeeـcreate()
    {
        return view('dashboard.admin.product.without-fee.create');
    }

    public function withoutـfeeـshow($productId)
    {
        $product = ProductWithoutFee::whereId(unHashId($productId))->firstorfail();

        return view('dashboard.admin.product.without-fee.edit', compact('product'));
    }
}
