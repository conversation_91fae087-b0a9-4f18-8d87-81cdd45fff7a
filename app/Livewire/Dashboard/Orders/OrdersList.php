<?php

namespace App\Livewire\Dashboard\Orders;

use App\Models\Sefaresh;
use Livewire\Component;

class OrdersList extends Component
{
    public array $data = [
        'MonthlyOne' => null,
        'MonthlyTow' => null,
        'Yearly' => null,
        'whatsapp' => null,
        'code' => null,
        'phone' => null,
        'address' => null,
        'category' => null,
        'type_construction' => null,
        'sex' => null,
        'fullname' => null,
        'last_status' => null,
        'name_pluck' => null,
        'model' => null,
        'chain' => null,
        'color' => null,
        'user_id' => null,
        'financial_status' => null,
        'code_order' => null,
        'postType' => null,
        'factor' => '',
        'count' => 0,
        'year' => '',
        'month' => '',
        'user' => '',
        'subscriber' => '',
        'filterDateBetweenChecked' => true,
        'eticket' => null,
        'userRefer' => null,
    ];

    public bool $search = false;

    public bool $top = false;

    public int $limitLoadOrder = 300;

    public function mount()
    {
        $this->data['MonthlyOne'] = verta()->month;
        $this->data['MonthlyTow'] = verta()->month;
        $this->data['Yearly'] = verta()->year;
    }

    #[Lazy]
    private function loadOrders()
    {

        $query = Sefaresh::with('userRecipient', 'user', 'factors');

        if ($this->data['filterDateBetweenChecked']) {
            $query->whereBetween('month', [$this->data['MonthlyOne'], $this->data['MonthlyTow']])->where('year', 'LIKE', $this->data['Yearly']);
        }

        if ($this->data['factor'] === null) {
            $query->where('whatsapp', '');
        } else {
            $query->where('whatsapp', '!=', null);
        }

        if (auth()->user()->level != 'admin') {
            $query->where('user_id', auth()->user()->id);
        }

        if ($this->data['code'] != null) {
            $query->where('code', 'like', '%'.$this->data['code'].'%');
        }

        if ($this->data['phone'] != null) {
            $query->where('phone', 'like', '%'.$this->data['phone'].'%');
        }

        if ($this->data['address'] != null) {
            $query->where('address', 'like', '%'.$this->data['address'].'%');
        }

        if ($this->data['category'] != null) {
            $query->where('type', $this->data['category']);
        }

        if ($this->data['type_construction'] != null) {
            $query->where('type_construction', $this->data['type_construction']);
        }

        if ($this->data['sex'] != null) {
            $query->where('sex', $this->data['sex']);
        }

        if ($this->data['fullname'] != null) {
            $query->where('fullname', 'like', '%'.$this->data['fullname'].'%');
        }

        if ($this->data['last_status'] != null) {
            $query->where('last_status', $this->data['last_status']);
        }

        if ($this->data['name_pluck'] != null) {
            $query->where('name_pluck', 'like', '%'.$this->data['name_pluck'].'%');
        }

        if ($this->data['model'] != null) {
            $query->where('model', 'like', '%'.$this->data['model'].'%');
        }

        if ($this->data['chain'] != null) {
            $query->where('chain', 'like', '%'.$this->data['chain'].'%');
        }

        if ($this->data['name_pluck'] != null) {
            $query->where('name_pluck', 'like', '%'.$this->data['name_pluck'].'%');
        }

        if ($this->data['color'] != null) {
            $query->where('color', $this->data['color']);
        }

        if ($this->data['whatsapp'] != null) {
            $query->where('whatsapp', $this->data['whatsapp']);
        }

        if ($this->data['financial_status'] != null) {
            $query->where('financial', $this->data['financial_status']);
        }

        if ($this->data['postType'] != null) {
            $query->where('post_type', $this->data['postType']);
        }

        if ($this->data['userRefer'] != null) {
            $query->where('user_id', $this->data['userRefer']);
        }

        if ($this->data['eticket'] != null) {
            $eticket = $this->data['eticket'];

            $query->where(function ($q) use ($eticket) {
                $q->whereHas('factors.factorItem', function ($q) use ($eticket) {
                    $q->where('eticket', $eticket);
                })->orWhereHas('invoice.productDetails', function ($q) use ($eticket) {
                    $q->where('eticket', $eticket);
                });
            });
        }

        $this->data['count'] = $query->count();

        return $query->latest()->paginate($this->limitLoadOrder);

    }

    public function fillter() {}

    public function ClearFillter()
    {
        $filters = [
            'code', 'phone', 'address', 'category', 'type_construction', 'sex',
            'fullname', 'last_status', 'name_pluck', 'model', 'chain', 'color', 'whatsapp',
            'financial_status', 'postType', 'eticket',
        ];

        foreach ($filters as $filter) {
            $this->data[$filter] = null;  // یا می‌توانید به جای null از '' استفاده کنید
        }
    }

    public function placeholder()
    {
        return view('livewire.dashboard.orders.orders-list-placeholder');
    }

    public function render()
    {
        return view('livewire.dashboard.orders.orders-list', [
            'orders' => $this->loadOrders(),
        ]);
    }
}
