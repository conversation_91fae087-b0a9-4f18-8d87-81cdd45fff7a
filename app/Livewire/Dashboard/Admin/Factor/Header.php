<?php

namespace App\Livewire\Dashboard\Admin\Factor;

use Livewire\Component;
use App\Models\FactorOrderEmpty;
use App\Models\FactorItemEmpty;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
class Header extends Component
{
    use LivewireAlert;

    public function factorClear(){
        try{

            DB::beginTransaction();

            FactorOrderEmpty::where('user_id', auth()->user()->id)->where('order_id', null)->delete();
            FactorItemEmpty::where('user_id', auth()->user()->id)->where('order_id', null)->delete();
            
            DB::commit();

            $this->alert('success', 'حذف با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            return redirect('/factor');

        }catch(\Exception $e){

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.factor.header');
    }
}
