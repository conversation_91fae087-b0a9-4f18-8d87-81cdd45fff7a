<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\Category;
use App\Models\Optional;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    #[Url]
    public $sortBy = 'newest_available';

    public array $data = [
        'minPrice' => 0,
        'maxPrice' => 5_000_000_000_000_000,
        'categories' => [],
        'branch' => [],
        'selectedCategories' => [],
        'selectedBranch' => [],
        'title_fa' => null,
        'eticket' => null,
        'inweight' => null,
        'outweight' => null,
        'products_all' => 0,
        'products_new_latest' => 0,
        'products_available' => 0,
        'products_out_of_stock' => 0,
        'total' => 0,
    ];

    public bool $group = false;

    protected $queryString = ['group'];

    public int $on_page = 18;

    public function toggleGroup()
    {
        $this->group = ! $this->group;
    }

    public function mount()
    {
        $this->data['categories'] = cache()->remember('categories', now()->addMinutes(10), fn () => Category::get());
        $this->data['branch'] = cache()->remember('branch_list', now()->addMinutes(30), fn () => Optional::where('type', 'branch')->get());
        $this->data['selectedBranch'] = $this->data['branch']->pluck('id')->toArray();
    }

    public function updateFilter($categoryTitle)
    {
        $this->resetPage();
        if (in_array($categoryTitle, $this->data['selectedCategories'])) {
            $this->data['selectedCategories'] = array_diff($this->data['selectedCategories'], [$categoryTitle]);
        } else {
            $this->data['selectedCategories'][] = $categoryTitle;
        }
    }

    public function sortByMethod($sortBy)
    {
        $this->resetPage();
        $this->sortBy = $sortBy;
    }

    public function sortGroup()
    {
        $this->group = ! $this->group;
    }

    public function changePriceBetween($minPrice, $maxPrice)
    {
        $this->resetPage();
        $this->data['minPrice'] = $minPrice;
        $this->data['maxPrice'] = $maxPrice;
    }

    public function search()
    {
        $this->resetPage();
    }

    #[On('products-group')]
    public function groupByToggle()
    {
        $this->group = ! $this->group;
    }

    public function ClearFillter()
    {
        $this->data['title_fa'] = null;
        $this->data['eticket'] = null;
        $this->data['inweight'] = null;
        $this->data['outweight'] = null;
        $this->data['minPrice'] = 0;
        $this->data['maxPrice'] = 5_000_000_000_000_000;
        $this->data['selectedCategories'] = [];
        $this->productAvailableList();
    }

    public function loadMore(): void
    {
        $this->on_page += 6;
    }

    private function productAvailableList()
    {
        $minPrice = (int) str_replace(',', '', $this->data['minPrice'] ?? '0');
        $maxPrice = (int) str_replace(',', '', $this->data['maxPrice'] ?? '5000000000000000');

        $selectedCategories = $this->data['selectedCategories'];
        $selectedBranch = $this->data['selectedBranch'];
        $title_fa = $this->data['title_fa'];
        $eticket = $this->data['eticket'];
        $inweight = $this->data['inweight'];
        $outweight = $this->data['outweight'];
        $sortBy = $this->sortBy;

        if (! $this->group) {
            $query = DB::table('product_details_view')
                ->select([
                    'product_detail_id',
                    'product_id',
                    'product_image_url',
                    'category_title_fa',
                    'updated_at',
                    'chain_size',
                    'product_title_fa',
                    'amount',
                    'available_stock',
                    'p_out_stock',
                    'pd_out_stock',
                    'category_id',
                    'eticket',
                    'weight',
                    'branch_id',
                    'fixed_amount',
                    'construction_wages',
                    'created_at',
                ])
                ->where(function ($q) use ($minPrice, $maxPrice) {
                    $q->whereRaw("CAST(REPLACE(REPLACE(amount, ',', ''), '.', '') AS DECIMAL(20,2)) >= ?", [$minPrice])
                        ->whereRaw("CAST(REPLACE(REPLACE(amount, ',', ''), '.', '') AS DECIMAL(20,2)) <= ?", [$maxPrice]);
                });
        } else {
            $query = DB::table('product_details_view')
                ->select([
                    DB::raw('MIN(product_detail_id) as product_detail_id'),
                    'product_id',
                    DB::raw('MAX(updated_at) as updated_at'),
                    DB::raw('MIN(amount) as min_amount'),
                    DB::raw('MAX(amount) as max_amount'),
                    DB::raw('MAX(product_image_url) as product_image_url'),
                    DB::raw('MAX(category_title_fa) as category_title_fa'),
                    DB::raw('MAX(chain_size) as chain_size'),
                    DB::raw('MAX(product_title_fa) as product_title_fa'),
                    DB::raw('COUNT(available_stock) as available_stock'),
                    DB::raw('COUNT(p_out_stock) as p_out_stock'),
                    DB::raw('COUNT(pd_out_stock) as pd_out_stock'),
                    DB::raw('MAX(category_id) as category_id'),
                    // DB::raw('MAX(eticket) as eticket'),
                    DB::raw('MAX(weight) as weight'),
                    DB::raw('MAX(branch_id) as branch_id'),
                    DB::raw('MAX(fixed_amount) as fixed_amount'),
                    DB::raw('MAX(construction_wages) as construction_wages'),
                    DB::raw('MAX(created_at) as created_at'),
                ])
                ->groupBy('product_id')
                ->where(function ($q) use ($minPrice, $maxPrice) {
                    $q->whereRaw("CAST(REPLACE(REPLACE(amount, ',', ''), '.', '') AS DECIMAL(18,2)) >= ?", [$minPrice])
                        ->whereRaw("CAST(REPLACE(REPLACE(amount, ',', ''), '.', '') AS DECIMAL(18,2)) <= ?", [$maxPrice]);
                });
        }

        $query
            ->when(! empty($selectedBranch), fn ($q) => $q->whereIn('branch_id', $selectedBranch))
            ->when(! empty($selectedCategories), fn ($q) => $q->whereIn('category_id', $selectedCategories))
            ->when(! empty($title_fa), fn ($q) => $q->where('product_title_fa', 'LIKE', "%{$title_fa}%"))
            ->when(! empty($eticket), fn ($q) => $q->where('eticket', 'LIKE', "%{$eticket}%"))
            ->when(! is_null($inweight) && ! is_null($outweight), fn ($q) => $q->whereBetween('weight', [$inweight, $outweight]));

        if ($sortBy !== 'all') {
            $this->applySortingToView($query, $sortBy);
        }

        $this->data['total'] = $query->count();

        return $query->paginate($this->on_page);
    }

    private function applySortingToView($query, $sortBy)
    {

        if (! $this->group) {

            return match ($sortBy) {
                'newest_available', 'available' => $query->havingRaw('available_stock > 0')
                    ->havingRaw('p_out_stock IS NULL')
                    ->havingRaw('pd_out_stock IS NULL')
                    ->orderBy('created_at', 'desc'),

                'most_expensive' => $query->orderBy(
                    DB::raw("CAST(REPLACE('amount', ',', '') AS DECIMAL(18,2))"),
                    'desc'
                ),

                'cheapest' => $query->orderBy(
                    DB::raw("CAST(REPLACE('amount', ',', '') AS DECIMAL(18,2))"),
                    'asc'
                ),

                'most_available' => $query->orderByRaw('COALESCE(available_stock, 0) DESC'),

                'least_available' => $query->orderByRaw('COALESCE(available_stock, 0) ASC'),

                'out_of_stock' => $query->havingRaw('available_stock = 0 AND (p_out_stock = 1 OR pd_out_stock = 1)')
                    ->orderBy('created_at', 'desc'),

                'reserved' => $query->havingRaw('available_stock = 0 AND p_out_stock IS NULL AND pd_out_stock IS NULL')
                    ->orderBy('created_at', 'desc'),

                'proven' => $query->havingRaw("fixed_amount IS NOT NULL AND fixed_amount != ''")
                    ->orderBy('created_at', 'desc'),

                'construction_wages' => $query->havingRaw('available_stock > 0')
                    ->havingRaw('p_out_stock IS NULL')
                    ->havingRaw('pd_out_stock IS NULL')
                    ->havingRaw('(fixed_amount IS NULL OR fixed_amount = 0)')
                    ->havingRaw('(construction_wages IS NULL OR construction_wages = 0)')
                    ->orderBy('created_at', 'desc'),

                default => $query->orderBy('created_at', 'desc'),
            };
        }

        return match ($sortBy) {
            'newest_available', 'available' => $query->havingRaw('available_stock - pd_out_stock > 0')
                ->orderBy('created_at', 'desc'),

            'most_expensive' => $query->orderByRaw("CAST(REPLACE(MAX(amount), ',', '') AS DECIMAL(18,2)) DESC"),

            'cheapest' => $query->orderByRaw("CAST(REPLACE(MIN(amount), ',', '') AS DECIMAL(18,2)) ASC"),

            'most_available' => $query->orderByRaw('COALESCE(available_stock, 0) DESC'),

            'least_available' => $query->orderByRaw('COALESCE(available_stock, 0) ASC'),

            'out_of_stock' => $query->havingRaw('available_stock - pd_out_stock = 0')->orderBy('created_at', 'desc'),

            'reserved' => $query->havingRaw('available_stock = 0 AND p_out_stock IS NULL AND pd_out_stock IS NULL')
                ->orderBy('created_at', 'desc'),

            'proven' => $query->havingRaw("fixed_amount IS NOT NULL AND fixed_amount != ''")
                ->orderBy('created_at', 'desc'),

            'construction_wages' => $query->havingRaw('available_stock > 0')
                ->havingRaw('p_out_stock IS NULL')
                ->havingRaw('pd_out_stock IS NULL')
                ->havingRaw('(fixed_amount IS NULL OR fixed_amount = 0)')
                ->havingRaw('(construction_wages IS NULL OR construction_wages = 0)')
                ->orderBy('created_at', 'desc'),

            'most_product_count' => $query->havingRaw('available_stock > 0')->orderBy('available_stock', 'desc'),

            default => $query->orderBy('created_at', 'desc'),
        };

    }

    public function render()
    {
        $this->data['minPrice'] = formatMoney($this->data['minPrice']);
        $this->data['maxPrice'] = formatMoney($this->data['maxPrice']);

        return view('livewire.dashboard.admin.shop.index', [
            'details' => $this->productAvailableList(),
        ]);
    }
}
