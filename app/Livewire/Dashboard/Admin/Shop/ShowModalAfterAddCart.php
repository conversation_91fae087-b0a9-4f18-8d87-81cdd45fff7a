<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\Checkout;
use App\Models\Checkout as Cart;
use App\Models\Resiver;
use App\Models\Sefaresh;
use App\Models\Setting;
use App\Models\Subscribe;
use App\Models\User;
use App\Service\Actions\Order\SetOfflineOrderAction;
use App\Service\Actions\Order\SetOnlineOrderAction;
use App\Service\CacheClear;
use Illuminate\Support\Facades\Artisan;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowModalAfterAddCart extends Component
{
    use LivewireAlert;

    #[Locked]
    public $baseUrlProduction;

    #[Locked]
    public $baseUrlTidamode;

    #[Locked]
    public $image_server_url;

    public $percentage = 0;

    public array $data = [
        'getUserDeatail' => false,
        'complate' => false,
        'orderId' => null,
        'factorId' => null,
        'factorCreate' => null,
        'total_factor' => 0,
        'codepost' => null,
        'fullname' => null,
        'totalWeight' => 0,
        'totalFactor' => 0,
        'phone' => '',
        'code' => '',
        'sex' => 'طلا',
        'post' => '',
        'category' => '',
        'customer_date' => '',
        'address' => '',
        'code_pigiri' => 0,
        'last_status' => 'money',
        'shortLink' => null,
        'description' => null,
        'paymentOnlineChecked' => true,
        'PostOnline' => false,
        'PostPayekOnline' => true,
        'MoneyPostOnline' => 0,
        'MoneyPostOnlineLabel' => 0,
        'userRefer' => null,
        'discount' => 0,

        'discount_code' => null,
        'discount_percentage' => null,
        'total_before_amount' => null,
        'profit_before' => null,
        'total_after_discount' => null,
        'profit_after' => null,

    ];

    public array $dataInvoice = [
        'product_count' => 0,
        'color' => '',
        'product_title' => '',
        'product_image_1' => '',
        'product_image_2' => '',
        'product_image_3' => '',
    ];

    public $user_address;

    public function backCheckout()
    {
        $this->data['getUserDeatail'] = false;
        // $this->data['fullname'] = null;
        // $this->data['phone'] = null;
        // $this->data['address'] = null;
        // $this->data['code'] = null;
        // $this->data['codepost'] = null;
        // $this->data['description'] = null;
        // $this->data['last_status'] = null;
        // $this->data['customer_date'] = null;
        // $this->data['userRefer'] = null;
        // $this->data['category'] = null;
        // $this->data['post'] = null;
    }

    public array $settings;

    public $users;

    public $carts;

    public $rateCount = 0;

    public function setLocation($address, $codepost, $fullname)
    {
        $this->data['fullname'] = $fullname;
        $this->data['address'] = $address;
        $this->data['codepost'] = $codepost;
    }

    public function rules()
    {
        return [
            'data.phone' => 'required',
            'data.fullname' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.phone.required' => 'پر کردن فیلد الزامیست',
            'data.fullname.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    #[On('set-discount-percentage')]
    public function getPercentage($discount)
    {

        $this->percentage = $discount ?? 0;
        $this->data['discount_code'] = $discount['code'] ?? null;
        $this->data['discount_percentage'] = $discount['percentage'] ?? null;

    }

    #[On('show-factor')]
    public function loadData()
    {
        $this->percentage = 0;
        $this->data['PostPayekOnline'] = true;
        $this->data['discount'] = null;

        $this->data['getUserDeatail'] = false;
        $this->data['fullname'] = null;
        $this->data['phone'] = null;
        $this->data['address'] = null;
        $this->data['code'] = null;
        $this->data['codepost'] = null;
        $this->data['description'] = null;
        $this->data['last_status'] = null;
        $this->data['customer_date'] = null;
        $this->data['userRefer'] = null;
        $this->data['category'] = null;
        $this->data['post'] = null;

        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL');
        $this->baseUrlTidamode = env('APP_ASSET_URL');
        $this->image_server_url = env('APP_ASSET_URL');
        $this->data['getUserDeatail'] = false;
        $this->data['shortLink'] = '';
        $this->data['complate'] = false;
        $this->data['factorCreate'] = isset($this->settings['factor']) && $this->settings['factor'] == 1 ? true : false;
        $this->data['MoneyPostOnline'] = formatMoney($this->settings['post_price']) ?? 0;

        $this->users = cache()->remember('users', 3600, function () {
            return User::select('id', 'level', 'mobile')
                ->whereIn('level', ['admin', 'user'])
                ->where('status', '1')
                ->get();
        });

    }

    public function updatedDataPaymentOnlineChecked()
    {

        if ($this->data['paymentOnlineChecked']) {
            $this->data['PostOnline'] = true;
            $this->data['PostPayekOnline'] = false;
            $this->data['MoneyPostOnline'] = formatMoney($this->settings['post_price']) ?? 0;
        } else {
            $this->data['PostOnline'] = false;
            $this->data['PostPayekOnline'] = false;
            $this->data['MoneyPostOnline'] = 0;
        }
    }

    public function updatedDataPostOnline()
    {
        if ($this->data['PostOnline']) {
            $this->data['PostPayekOnline'] = false;
            $this->data['MoneyPostOnline'] = '0';
        } else {
            $this->data['PostPayekOnline'] = true;
            $this->data['MoneyPostOnline'] = formatMoney($this->settings['post_price']) ?? 0;
        }
    }

    public function updatedDataPostPayekOnline()
    {
        if ($this->data['PostPayekOnline']) {
            $this->data['PostOnline'] = false;
            $this->data['MoneyPostOnline'] = formatMoney($this->settings['post_price']) ?? 0;
        } else {
            $this->data['PostOnline'] = true;
            $this->data['MoneyPostOnline'] = '0';
        }
    }

    public function removeItem($itemId)
    {

        Checkout::where('user_id', auth()->user()->id)->where('id', $itemId)->delete();
        CacheClear::clearKeys(['checkout_'.auth()->user()->id, 'checkout_cart_count_'.auth()->user()->id]);

        $this->dispatch('show-factor');
    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        $user = Subscribe::whereId($userId)->first();
        if (isset($user) && $user != null) {
            $this->data['fullname'] = $user?->fullname;
            $this->data['phone'] = $user?->mobile;
            $this->data['code'] = $user?->code;
            $this->data['codepost'] = $user?->codepost;
            $this->data['address'] = $user?->address;
        }

    }

    public function stepTow()
    {

        $this->data['getUserDeatail'] = true;
    }

    public function updatedDataPhone()
    {
        $this->data['fullname'] = null;
        $this->data['codepost'] = null;
        $this->data['address'] = null;

        if (! empty($this->data['phone'])) {
            $query = Sefaresh::where('phone', $this->data['phone']);
            $this->rateCount = $query->count(); // تعداد را قبل از لود داده‌ها می‌گیریم

            $orders = $query->get()->map(function ($item) {
                return (object) [
                    'fullname' => $item->fullname,
                    'address' => $item->address,
                    'phone' => $item->phone,
                    'codepost' => $item->codepost,
                ];
            });

            $resivers = Resiver::where('mobile', $this->data['phone'])->get()->map(function ($item) {
                return (object) [
                    'fullname' => $item->fullname,
                    'address' => $item->address,
                    'phone' => $item->mobile, // یا هر فیلدی که لازم دارید
                    'codepost' => $item->codepost,

                ];
            });

            $user_address = $orders->merge($resivers)->unique(function ($item) {
                return $item->address.'|'.$item->phone.'|'.$item->codepost.'|'.$item->fullname;
            })->values();

            $this->user_address = $user_address;
        } else {
            $this->user_address = null;
        }

        // $this->data['phone'] = PhoneNumberFix($this->data['phone']);
        // $phone = PhoneNumberFix($this->data['phone']);
        // if ($phone != null) {
        //     $subscriber = Subscribe::where('mobile', $phone)->first();
        //     $this->data['fullname'] = $subscriber?->fullname;
        //     // $this->data['codepost'] = $subscriber?->codepost;
        //     $this->data['code'] = $subscriber?->code;
        //     // $this->data['address'] = $subscriber?->address;
        // } else {
        //     $this->data['fullname'] = null;
        //     $this->data['codepost'] = null;
        //     $this->data['code'] = null;
        //     $this->data['address'] = null;
        // }
    }

    public function createFactor()
    {

        $this->validate();

        if ($this->data['paymentOnlineChecked']) {
            $this->createInvoiceOnline();
        } else {
            $this->createOrderOffline();
        }

        Artisan::call('optimize:clear');

    }

    public function calcTotalCheckoutUser()
    {
        $checkouts = Checkout::with('product', 'details')->where('user_id', auth()->user()->id)->get();
        $this->data['totalFactor'] = 0;
        $this->data['totalWeight'] = 0;

        $calculatedDetails = [];

        foreach ($checkouts as $item) {
            if ($item->product->fixed_amount != null) {
                // $amount = str_replace(',', '', $item->product->fixed_amount);
                // $this->data['totalFactor'] += (float) $amount * $item->count;
            } else {
                // $amount = str_replace(',', '', $item->detail->amount);
                // $this->data['totalFactor'] += (float) $amount * $item->count;

                // اگر قبلاً محاسبه نشده بود، وزن را اضافه کن
                if ($item->detail && ! in_array($item->detail->id, $calculatedDetails)) {
                    $this->data['totalWeight'] += $item->detail->weight;
                    $calculatedDetails[] = $item->detail->id;
                }
            }
        }

        $checkoutSum = (float) str_replace(',', '', auth()->user()->sumCheckout());
        $post = (float) str_replace(',', '', $this->data['MoneyPostOnline']);
        $this->data['totalFactor'] = ($checkoutSum + $post);

        // $this->data['totalFactor'] = auth()->user()->sumCheckout();
        // $this->data['discount'] = $this->discount;

        // if ($this->data['paymentOnlineChecked']) {
        // $amountPost = str_replace(',', '', $this->data['MoneyPostOnline']);
        // $this->data['totalFactor'] += (float) $amountPost;
        // }

    }

    private function createInvoiceOnline()
    {

        $this->data['discount'] = $this->percentage > 0
                    ? auth()->user()->sumCheckout() -
                        auth()->user()->sumDiscountCheckout($this->percentage)
                    : 0;

        $result = SetOnlineOrderAction::handle($this->data, auth()->user()->id, auth()->user()->mobile, auth()->user()->fullname, $this->baseUrlProduction);
        if ($result['status'] == true) {
            $this->data['code_pigiri'] = $result['HashInvoiceId'];
            $this->data['orderId'] = $result['HashInvoiceId'];
            $this->data['factorId'] = $result['invoiceId'];

            $this->backCheckout();
            $this->checkoutClear();
            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->data['shortLink'] = $result['shotLinkUrl'];
            $this->data['complate'] = true;

            return;
        }

        $this->alert('error', 'خطا در ثبت', [
            'position' => 'center',
            'timer' => 30000,
            'toast' => false,
            'text' => $result['message'],
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

    }

    public function createOrderOffline()
    {

        $this->data['PostOnline'] = true;
        $this->data['MoneyPostOnline'] = '0';

        $this->data['discount'] = $this->percentage > 0
                    ? auth()->user()->sumCheckout() -
                        auth()->user()->sumDiscountCheckout($this->percentage)
                    : 0;

        $result = SetOfflineOrderAction::handle($this->data, auth()->user()->id, auth()->user()->mobile, auth()->user()->fullname, $this->baseUrlProduction);
        if ($result['status'] == true) {

            $this->data['orderId'] = $result['orderId'];

            $this->backCheckout();

            $this->checkoutClear();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->data['complate'] = true;

            return;
        }

        $this->alert('error', 'خطا در ثبت', [
            'position' => 'center',
            'timer' => 30000,
            'toast' => false,
            'text' => $result['message'],
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

    }

    public function checkoutClear()
    {
        Cart::where('user_id', auth()->user()->id)->delete();
        CacheClear::clearKeys(['checkout_'.auth()->user()->id, 'checkout_cart_count_'.auth()->user()->id]);
        $this->percentage = 0;
        // $this->dispatch('show-factor');
    }

    public function render()
    {

        $this->calcTotalCheckoutUser();
        $this->carts = cache()->remember('checkout_'.auth()->user()->id, 300, function () {
            return Checkout::with('product.gallery', 'details')->where('user_id', auth()->user()->id)->get();
        });

        $this->settings = cache()->remember('settings_post_price', 300, function () {
            return Setting::whereIn('type', [
                'post_price',
                'factor',
            ])->pluck('body', 'type')->toArray();
        });

        $this->data['MoneyPostOnlineLabel'] = formatMoney($this->settings['post_price']) ?? 0;

        return view('livewire.dashboard.admin.shop.show-modal-after-add-cart');
    }
}
