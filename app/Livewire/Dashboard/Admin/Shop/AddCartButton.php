<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\Checkout as Cart;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Service\CacheClear;
use Illuminate\Support\Facades\Artisan;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class AddCartButton extends Component
{
    use LivewireAlert;

    public $productId;

    public $weightId;

    public $count = 1;

    public $maxCount = 1;

    public function increment()
    {
        $cart = Cart::where('user_id', auth()->user()->id)->where('product_id', $this->productId)->where('weight_id', $this->weightId)->first();
        if ($cart->count <= $this->maxCount) {
            $cart->update([
                'count' => $cart->count + 1,
            ]);
        }

        CacheClear::clearKeys(['checkout_'.auth()->user()->id, 'checkout_cart_count_'.auth()->user()->id]);

    }

    public function mount() {}

    public function addCart()
    {
        $product = Product::with('ProductCount')->whereId($this->productId)->first();

        if ((int) $this->maxCount < (int) $this->count) {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'محصول '.$product->title_fa.' موجودی ندارد لطفا سبد خرید خود را مجدد چک کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
            Artisan::call('optimize:clear');

            return;
        }



        $cart = Cart::where('user_id', auth()->user()->id)
            ->where('product_id', $this->productId)
            ->where('weight_id', $this->weightId)
            ->first();

        if (! $cart) {

            // در غیر این صورت، یک رکورد جدید ایجاد کن
            Cart::create([
                'user_id' => auth()->user()->id,
                'product_id' => $this->productId,
                'weight_id' => $this->weightId,
                'count' => 1,
            ]);
        }

        // پاک کردن کش‌های مرتبط
        CacheClear::clearKeys(['checkout_'.auth()->user()->id, 'checkout_cart_count_'.auth()->user()->id]);

        // ارسال رویداد برای نمایش فاکتور
        $this->dispatch('hide-product-details');
        $this->dispatch('show-factor');
    }

    public function removeItem()
    {

        $product = Product::whereId($this->productId)->first();
        if ($product->fixed_amount != null) {
            $this->count = $product->getProductCount();
        } else {
            $productDetail = ProductDetail::where('product_id', $this->productId)->whereId($this->weightId)->first();
            $this->count = $productDetail->getProductDetailCount();
        }

        $cart = Cart::where('user_id', auth()->user()->id)
            ->where('product_id', $this->productId)
            ->where('weight_id', $this->weightId)
            ->first();

        if ($cart) {
            if ($cart->count > 1) {
                $cart->update(['count' => $cart->count - 1]);
            } else {
                $cart->delete();
            }
        }

        CacheClear::clearKeys(['checkout_'.auth()->user()->id, 'checkout_cart_count_'.auth()->user()->id]);
        $this->dispatch('hide-product-details');
        $this->dispatch('show-factor');
    }

    public function render()
    {

        $carts = cache()->get('checkout_'.auth()->user()->id);

        if ($carts) {

            $cart = collect($carts)->firstWhere(fn ($item) => $item['product_id'] == $this->productId && $item['weight_id'] == $this->weightId);

            return view('livewire.dashboard.admin.shop.add-cart-button', [
                'cart' => $cart,
            ]);
        }

        $cart = Cart::where('product_id', $this->productId)
            ->where('weight_id', $this->weightId)
            ->first();

        if ($cart) {
            $carts = cache()->remember('checkout_'.auth()->user()->id, 60 * 24 * 365, function () {
                return Cart::with('product.gallery', 'details')->where('user_id', auth()->user()->id)->get();
            });
        }

        return view('livewire.dashboard.admin.shop.add-cart-button', [
            'cart' => $cart,
        ]);
    }
}
