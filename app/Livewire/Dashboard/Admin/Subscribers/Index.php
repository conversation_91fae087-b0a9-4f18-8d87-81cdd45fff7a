<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use Livewire\Component;
use App\Models\Subscribe;
use Livewire\WithPagination;
use Livewire\Attributes\On; 
class Index extends Component
{
    use WithPagination;
    
    public $code, $fullname, $phone, $address;

    #[On('subscribers-reload')]
    public function reload(){

    }

    #[On('subscribe-filter')]
    public function loadData($data){

        if (isset($data) && is_array($data)) {
            $this->code = $data['code'] ?? null;
            $this->fullname = $data['fullname'] ?? null;
            $this->phone = $data['phone'] ?? null;
            $this->address = $data['address'] ?? null;
        }
    }

    public function render()
    {
        $query = Subscribe::query();

        if ($this->code) {
            $query->where('code', 'like', '%' . $this->code . '%');
        }

        if ($this->fullname) {
            $query->where('fullname', 'like', '%' . $this->fullname . '%');
        }

        if ($this->phone) {
            $query->where('mobile', 'like', '%' . $this->phone . '%');
        }

        if ($this->address) {
            $query->where('address', 'like', '%' . $this->address . '%');
        }

        $users = $query->latest()->paginate(10);

        return view('livewire.dashboard.admin.subscribers.index',[
            'users' => $users
        ]);
    }
}
