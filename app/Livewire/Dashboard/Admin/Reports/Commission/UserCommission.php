<?php

namespace App\Livewire\Dashboard\Admin\Reports\Commission;

use App\Models\Financial;
use App\Models\Sefaresh;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class UserCommission extends Component
{
    public $data = [
        'Monthly' => '',
        'Yearly' => '',
        'user_id' => '',
    ];

    public function mount()
    {
        $date = verta();
        $this->data['Monthly'] = $date->month;
        $this->data['Yearly'] = $date->year;
    }

    public function search() {}

    public function render()
    {

        $cacheKey = "commission_data_{$this->data['Monthly']}_{$this->data['user_id']}_{$this->data['Yearly']}";

        $commissionData = Cache::remember($cacheKey, now()->addMinutes(15), function () {
            $orders = Sefaresh::where('user_id', $this->data['user_id'])->where('month', $this->data['Monthly'])
                ->where('year', $this->data['Yearly'])
                ->where('last_status', '!=', 'cancel')
                ->get(['id', 'total_amount', 'deposit1', 'deposit2']);

            $commission = Financial::whereIn('sefaresh_id', $orders->pluck('id'))->sum('commission');

            $totalAmount = $orders->sum('total_amount');
            $totalCountOrder = $orders->count();
            $deposit1 = $orders->sum('deposit1');
            $deposit2 = $orders->sum('deposit2');
            $remaining = $totalAmount - ($deposit1 + $deposit2);

            return compact('commission', 'totalAmount', 'deposit1', 'deposit2', 'remaining', 'totalCountOrder');
        });

        $users = Cache::remember('users', now()->addMinutes(60), function () {
            return User::whereIn('level', ['user', 'admin'])->where('status', 1)->get();
        });

        return view('livewire.dashboard.admin.reports.commission.user-commission', [
            'commission' => $commissionData['commission'],
            'totalAmount' => $commissionData['totalAmount'],
            'deposit1' => $commissionData['deposit1'],
            'deposit2' => $commissionData['deposit2'],
            'remaining' => $commissionData['remaining'],
            'totalCountOrder' => $commissionData['totalCountOrder'],
            'users' => $users,
        ]);
    }
}
