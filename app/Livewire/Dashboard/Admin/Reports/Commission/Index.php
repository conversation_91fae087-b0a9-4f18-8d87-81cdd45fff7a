<?php

namespace App\Livewire\Dashboard\Admin\Reports\Commission;

use App\Models\Financial;
use App\Models\Sefaresh;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class Index extends Component
{
    public $data = [
        'MonthlyOne' => '',
        'MonthlyTow' => '',
        'Yearly' => '',
    ];

    public function mount()
    {
        $date = verta();
        $this->data['MonthlyOne'] = '1';
        $this->data['MonthlyTow'] = '12';
        $this->data['Yearly'] = $date->year;
    }

    public function search() {}

    public function render()
    {

        $cacheKey = "commission_data_{$this->data['MonthlyOne']}_{$this->data['MonthlyTow']}_{$this->data['Yearly']}";

        $commissionData = Cache::remember($cacheKey, now()->addMinutes(15), function () {
            $orders = Sefaresh::whereBetween('month', [$this->data['MonthlyOne'], $this->data['MonthlyTow']])
                ->where('year', $this->data['Yearly'])
                ->where('last_status', '!=', 'cancel')
                ->get(['id', 'total_amount', 'deposit1', 'deposit2']); // گرفتن فقط فیلدهای موردنیاز

            $commission = Financial::whereIn('sefaresh_id', $orders->pluck('id'))->sum('commission');

            $totalAmount = $orders->sum(function ($order) {
                return str_replace(',', '', $order->total_amount);
            });

            $deposit1 = $orders->sum(function ($order) {
                return str_replace(',', '', $order->deposit1);
            });

            $deposit2 = $orders->sum(function ($order) {
                return str_replace(',', '', $order->deposit2);
            });

            $remaining = $totalAmount - ($deposit1 + $deposit2);

            return compact('commission', 'totalAmount', 'deposit1', 'deposit2', 'remaining');
        });

        return view('livewire.dashboard.admin.reports.commission.index', [
            'commission' => $commissionData['commission'],
            'totalAmount' => $commissionData['totalAmount'],
            'deposit1' => $commissionData['deposit1'],
            'deposit2' => $commissionData['deposit2'],
            'remaining' => $commissionData['remaining'],
        ]);
    }
}
