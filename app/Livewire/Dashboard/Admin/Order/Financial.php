<?php

namespace App\Livewire\Dashboard\Admin\Order;

use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial as Finan;
use Illuminate\Support\Facades\DB;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Financial extends Component
{
    use LivewireAlert;

    public $order;

    public $package_amount;

    public $chain;

    public $amount_ready;

    public $designing;

    public $sender;

    public $packing;

    public $commission;

    public $total_amount;

    public $sefaresh_total;

    public $profit;

    public $tipsGold;

    public $FinancialLock = true;

    public function mount()
    {

        $this->sefaresh_total = formatMoney($this->order->sefaresh_total);

        $financial = Finan::where('sefaresh_id', $this->order->id)->first();
        if (isset($financial) && $financial != null) {
            $this->package_amount = formatMoney($financial->package_amount);
            $this->chain = formatMoney(money: $financial->chain);
            $this->amount_ready = formatMoney($financial->amount_ready);
            $this->designing = formatMoney($financial->designing);
            $this->sender = formatMoney($financial->sender);
            $this->packing = formatMoney($financial->packing);
            $this->commission = formatMoney($financial->commission);
            $this->tipsGold = $financial->tipsGold;
            $this->FinancialLock = $this->order->financial == 1 ? true : false;
            $this->profit = formatMoney($financial->profit);
        }

    }

    // private function calc()
    // {
    // $package_amount = $this->package_amount != null ? str_replace(',', '', $this->package_amount) : 0;
    // $chain = $this->chain != null ? str_replace(',', '', $this->chain) : 0;
    // $amount_ready = $this->amount_ready != null ? str_replace(',', '', $this->amount_ready) : 0;
    // $designing = $this->designing != null ? str_replace(',', '', $this->designing) : 0;
    // $sender = $this->sender != null ? str_replace(',', '', $this->sender) : 0;
    // $packing = $this->packing != null ? str_replace(',', '', $this->packing) : 0;
    // $sefaresh_total = $this->sefaresh_total != null ? str_replace(',', '', $this->sefaresh_total) : 0;
    // $commission = $this->commission != null ? str_replace(',', '', $this->commission) : 0;
    // $total = $package_amount + $amount_ready + $designing + $sender + $packing + $chain;
    // $negative = $sefaresh_total - $total - $commission;
    // $this->total_amount = number_format($total);
    // $this->profit = number_format($negative);
    // return response()->json(['success'=>['calc',number_format($total),number_format($negative)]]);
    // }

    public function store()
    {

        try {

            DB::beginTransaction();
            // $this->calc();

            $financials = Finan::updateOrCreate(
                ['sefaresh_id' => $this->order->id],
                [
                    'user_id' => auth()->user()->id,
                    'sefaresh_id' => $this->order->id,
                    // 'receiver_order' => $this->receiver_order,
                    'chain' => (float) str_replace(',', '', $this->chain),
                    'package_amount' => (float) str_replace(',', '', $this->package_amount),
                    'amount_ready' => (float) str_replace(',', '', $this->amount_ready),
                    'designing' => (float) str_replace(',', '', $this->designing),
                    'sender' => (float) str_replace(',', '', $this->sender),
                    'total_amount' => $this->total_amount,
                    'commission' => (float) str_replace(',', '', $this->commission),
                    'packing' => (float) str_replace(',', '', $this->packing),
                    'profit' => (float) str_replace(',', '', $this->profit),
                    'tipsGold' => $this->tipsGold,
                ]
            );

            $order = \App\Models\Sefaresh::findorfail($this->order->id);
            $this->FinancialLock == true ? $order->financial = 1 : $order->financial = 0;
            $order->save();
            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function calc()
    {
        try {
            $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            if ($factor) {
                $weight = FactorItemEmpty::where('order_id', $this->order->id)
                    ->whereNotNull('weight')
                    ->sum('weight');

                $this->commission = calcCommission($weight, $factor->order->gold18k);

                $this->profit = $weight * (float) str_replace(',', '', $factor->order->gold18k) * 7 / 10;

                $this->store();

                $this->profit = formatMoney($this->profit);
                $this->commission = formatMoney($this->commission);

            }
        } catch (\Exception $e) {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'محاسبه هوشمند مالی برای این سفارش با مشکل مواجه شده. دقت بفرمایید اطلاعات فاکتور این سفارش به درستی پر شده باشند',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.order.financial');
    }
}
