<?php

namespace App\Livewire\Dashboard\Admin\Order;

use App\Models\Financial;
use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Resiver;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\StatusSms;
use App\Models\Subscribe;
use App\Models\Tracking;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class Show extends Component
{
    use LivewireAlert;
    use WithFileUploads;

    public $order;

    public array $data = [
        'category' => null,
        'type' => null,
        'type_construction' => null,
        'negative' => null,
        'model' => null,
        'sex' => null,
        'color' => null,
        'post_type' => null,
        'total_amount' => null,
        'package_type' => null,
        'fullname' => null,
        'phone' => null,
        'address' => null,
        'codepost' => null,
        'last_status' => null,
        'date_last_status' => null,
        'order_register_date' => null,
        'rCodePost' => null,
        'whatsapp' => null,
        'deposit1' => null,
        'deposit2' => null,
        'package_amount' => null,
        'name_pluck' => null,
        'chain' => null,
        'size' => null,
        'size_wrist' => null,
        'size_ankle' => null,
        'designer_id' => null,
        'manufacturer_id' => null,
        'dimensions' => null,
        'font' => null,
        'tracking_code' => null,
        'customer_date' => null,
        'image1' => null,
        'image2' => null,
        'image3' => null,
        'image4' => null,
        'image5' => null,
        'image6' => null,
        'image7' => null,
        'total' => null,
        'remaining' => null,
        'gram' => null,
        'gold18k' => null,
        'chResiver' => false,
        'r_fullname' => null,
        'r_codepost' => null,
        'sefaresh_total' => null,
        'phone_whatsapp' => null,
        'product_code' => null,
        'fullname_agent' => null,
        'phone_agent' => null,
    ];

    public $settings;

    public function mount()
    {

        $this->data['type_construction'] = $this->order->type_construction;
        $this->data['rCodePost'] = $this->order->codepost;
        $this->data['model'] = $this->order->model;
        $this->data['sex'] = $this->order->sex;
        $this->data['color'] = $this->order->color;
        $this->data['font'] = $this->order->font;
        $this->data['name_pluck'] = $this->order->name_pluck;
        $this->data['post_type'] = $this->order->post_type;
        $this->data['tracking_code'] = $this->order->tracking_code;
        $this->data['tips'] = $this->order->tips;
        $this->data['total_amount'] = formatMoney($this->order->total_amount);
        $this->data['deposit1'] = formatMoney($this->order->deposit1);
        $this->data['deposit2'] = formatMoney($this->order->deposit2);
        $this->data['remaining'] = formatMoney($this->order->remaining);
        $this->data['sefaresh_total'] = $this->order->sefaresh_total;
        $this->data['package_type'] = $this->order->package_type;
        $this->data['package_amount'] = $this->order->package_amount;
        $this->data['fullname'] = $this->order->fullname;
        $this->data['phone'] = $this->order->phone;
        $this->data['address'] = $this->order->address;
        $this->data['codepost'] = $this->order->codepost;
        $this->data['chain'] = $this->order->chain;
        $this->data['size'] = $this->order->size;
        $this->data['size_wrist'] = $this->order->size_wrist;
        $this->data['size_ankle'] = $this->order->size_ankle;
        $this->data['order_register_date'] = $this->order->order_register_date;
        $this->data['customer_date'] = $this->order->customer_date;
        $this->data['last_status'] = $this->order->last_status;
        $this->data['date_last_status'] = $this->order->date_last_status;
        $this->data['whatsapp'] = $this->order->whatsapp;
        $this->data['phone_whatsapp'] = $this->order->phone_whatsapp;
        $this->data['product_code'] = $this->order->product_code;
        $this->data['designer_id'] = $this->order->designer_id;
        $this->data['manufacturer_id'] = $this->order->manufacturer_id;
        $this->data['dimensions'] = $this->order->dimensions;
        $this->data['gram'] = $this->order->gram;
        $this->data['gold18k'] = formatMoney($this->order->gold18k);

        $this->data['fullname_agent'] = $this->order->fullname_agent;
        $this->data['phone_agent'] = $this->order->phone_agent;

        $image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        $image_server_url_2 = env('APP_ASSET_URL');

        $this->data['image1'] = $this->getValidImageUrl($this->order->image1, $image_server_url_1, $image_server_url_2);
        $this->data['image2'] = $this->getValidImageUrl($this->order->image2, $image_server_url_1, $image_server_url_2);
        $this->data['image3'] = $this->getValidImageUrl($this->order->image3, $image_server_url_1, $image_server_url_2);
        $this->data['image4'] = $this->getValidImageUrl($this->order->image4, $image_server_url_1, $image_server_url_2);
        $this->data['image5'] = $this->getValidImageUrl($this->order->image5, $image_server_url_1, $image_server_url_2);
        $this->data['image6'] = $this->getValidImageUrl($this->order->image6, $image_server_url_1, $image_server_url_2);
        $this->data['image7'] = $this->getValidImageUrl($this->order->image7, $image_server_url_1, $image_server_url_2);

        $this->data['r_fullname'] = isset($this->order->resiver) ? $this->order->resiver->fullname : '';
        $this->data['r_phone'] = isset($this->order->resiver) ? $this->order->resiver->mobile : '';
        $this->data['r_codepost'] = isset($this->order->resiver) ? $this->order->resiver->codepost : '';
        $this->data['r_address'] = isset($this->order->resiver) ? $this->order->resiver->address : '';
        $this->data['chResiver'] = isset($this->order->resiver) ? true : false;

        $this->calc();

        $this->calcFactor();
    }

    public function getValidImageUrl($imageName, $image_server_url_1, $image_server_url_2)
    {

        if (! $imageName) {
            return '';
        }

        // بررسی آدرس اول
        $url1 = $image_server_url_1.$imageName;
        $headers = @get_headers($url1);
        if ($headers && strpos($headers[0], '200')) {
            return $url1;
        }

        // بررسی آدرس دوم
        $url2 = $image_server_url_2.$imageName;
        $headers = @get_headers($url2);
        if ($headers && strpos($headers[0], '200')) {
            return $url2;
        }

        // بررسی آدرس دوم + storage
        $url3 = $image_server_url_2.'storage/'.$imageName;
        $headers = @get_headers($url3);
        if ($headers && strpos($headers[0], '200')) {
            return $url3;
        }

        // fallback
        return 'https://shop.tidamode.ir/'.$imageName;
    }

    public function rules()
    {
        return [
            // 'data.type' => 'required',
            // 'data.gram' => 'required',
            'data.type_construction' => 'required',
            'data.model' => 'required',
            'data.sex' => 'required',
            'data.color' => 'required',
            'data.post_type' => 'required',
            'data.total_amount' => 'required',
            'data.package_type' => 'required',
            'data.fullname' => 'required',
            'data.phone' => 'required|min:11',
            'data.address' => 'required',
            'data.codepost' => 'required',
            'data.last_status' => 'required',
            'data.date_last_status' => 'required',
            'data.order_register_date' => 'required',
            'data.whatsapp' => 'required',
            // 'image1' => 'required|max:1536|mimes:jpg,png,jpeg',
            // 'image3' => 'required|max:1536|mimes:jpg,png,jpeg',
            // 'image6' => 'required|max:1536|mimes:jpg,png,jpeg',
        ];
    }

    public function messages()
    {
        return [
            // 'data.type.required' => 'پر کردن فیلد الزامیست',
            // 'data.gram.required' => 'پر کردن فیلد الزامیست',
            // 'gram.numeric' => 'مقدار وارد شده برای فیلد گرم باید یک عدد باشد.',
            // 'gram.regex' => 'مقدار وارد شده برای فیلد گرم باید یک عدد اعشاری معتبر باشد.',
            'data.type_construction.required' => 'پر کردن فیلد الزامیست',
            'data.model.required' => 'پر کردن فیلد الزامیست',
            'data.sex.required' => 'پر کردن فیلد الزامیست',
            'data.color.required' => 'پر کردن فیلد الزامیست',
            'data.post_type.required' => 'پر کردن فیلد الزامیست',
            'data.total_amount.required' => 'پر کردن فیلد الزامیست',
            'data.package_type.required' => 'پر کردن فیلد الزامیست',
            'data.fullname.required' => 'پر کردن فیلد الزامیست',
            'data.phone.required' => 'پر کردن فیلد الزامیست',
            'data.address.required' => 'پر کردن فیلد الزامیست',
            'data.codepost.required' => 'پر کردن فیلد الزامیست',
            'data.last_status.required' => 'پر کردن فیلد الزامیست',
            'data.date_last_status.required' => 'پر کردن فیلد الزامیست',
            'data.order_register_date.required' => 'پر کردن فیلد الزامیست',
            'data.whatsapp.required' => 'پر کردن فیلد الزامیست',
            // 'image1.required' => 'بارگزاری فایل الزامیست',
            // 'image3.required' => 'بارگزاری فایل الزامیست',
            // 'image6.required' => 'بارگزاری فایل الزامیست',
        ];
    }

    protected $listeners = ['webcam-captured-1' => 'handleWebcamCapture'];

    public function handleWebcamCapture($image)
    {
        try {
            $this->data['image1'] = null;
            // پردازش تصویر base64
            $base64Image = str_replace('data:image/png;base64,', '', $image); // توجه: $image باید آرایه باشد
            $base64Image = str_replace(' ', '+', $base64Image);
            $imageBinary = base64_decode($base64Image);

            // ایجاد نام فایل
            $fileName = 'webcam_'.time().'.png';

            // مسیر کامل ذخیره‌سازی (با نام فایل)
            $directory = storage_path('app/public/uploads/webcam');
            $fullPath = $directory.'/'.$fileName;

            // ایجاد دایرکتوری اگر وجود ندارد
            // if (! file_exists($directory)) {
            //     mkdir($directory, 0755, true);
            // }

            // ذخیره فایل
            file_put_contents($fullPath, $imageBinary);

            // مسیر نسبی برای دیتابیس
            $relativePath = 'uploads/webcam/'.$fileName;

            $this->data['image1'] = '/storage/'.$relativePath;
            // dd($relativePath);

            return $relativePath;

        } catch (\Exception $e) {

            dd($e->getMessage());
            // \Log::error('Webcam Error: '.$e->getMessage());
            // $this->dispatch('error', message: 'خطا در ذخیره تصویر');

            // return false;
        }
    }

    public function store(Request $req)
    {
        // dd($this->data['image1']);

        // $this->validate();

        $rCodePost = str_replace('-', '', $this->data['codepost']);

        // $rPhone = fixPhoneNumber($this->data['phone']);
        // if (! preg_match('/^09[0-9]{9}$/', $rPhone)) {
        //     return response()->json(['error' => ['شماره موبایل سفارش دهنده را بصورت صحیح وارد نمایید']]);
        // }

        try {

            DB::beginTransaction();

            $total_amount = str_replace(',', '', $this->data['total_amount']);
            $deposit1 = str_replace(',', '', $this->data['deposit1']);
            $deposit2 = str_replace(',', '', $this->data['deposit2']);
            $package_amount = str_replace(',', '', $this->data['package_amount']);

            $total_amount = $total_amount == '' ? 0 : $total_amount;
            $deposit1 = $deposit1 == '' ? 0 : $deposit1;
            $deposit2 = $deposit2 == '' ? 0 : $deposit2;
            $package_amount = $package_amount == '' ? 0 : $package_amount;

            $total = (float) $total_amount + (float) $package_amount;
            $negative = (float) $total - (float) $deposit1 - (float) $deposit2;

            Sefaresh::whereId($this->order->id)->update(
                [
                    'type_construction' => $this->data['type_construction'],
                    'model' => $this->data['model'],
                    'sex' => $this->data['sex'],
                    'color' => $this->data['color'],
                    'font' => $this->data['font'],
                    'name_pluck' => $this->data['name_pluck'],
                    'post_type' => $this->data['post_type'],
                    'tracking_code' => $this->data['tracking_code'],
                    'tips' => $this->data['tips'],
                    'total_amount' => str_replace(',', '', $total_amount),
                    'deposit1' => str_replace(',', '', $deposit1),
                    'deposit2' => str_replace(',', '', $deposit2),
                    'remaining' => str_replace(',', '', $negative),
                    'sefaresh_total' => str_replace(',', '', $total_amount),
                    'package_type' => $this->data['package_type'],
                    'package_amount' => $package_amount,

                    'fullname' => $this->data['fullname'],
                    'phone' => $this->data['phone'],
                    'address' => $this->data['address'],
                    'codepost' => $this->data['codepost'],

                    'chain' => $this->data['chain'],
                    'size' => $this->data['size'],
                    'size_wrist' => $this->data['size_wrist'],
                    'size_ankle' => $this->data['size_ankle'],

                    'order_register_date' => $this->data['order_register_date'],
                    'customer_date' => $this->data['customer_date'],
                    'last_status' => $this->data['last_status'],
                    'date_last_status' => $this->data['date_last_status'],

                    'whatsapp' => $this->data['whatsapp'],
                    'phone_whatsapp' => '0',
                    'product_code' => '0',

                    'designer_id' => $this->data['designer_id'],
                    'manufacturer_id' => $this->data['manufacturer_id'],
                    'dimensions' => $this->data['dimensions'],

                    'image1' => $this->data['image1'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image1'])
                        : $this->replaceUrls($this->data['image1']),

                    'image2' => $this->data['image2'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image2'])
                        : $this->replaceUrls($this->data['image2']),

                    'image3' => $this->data['image3'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image3'])
                        : $this->replaceUrls($this->data['image3']),

                    'image4' => $this->data['image4'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image4'])
                        : $this->replaceUrls($this->data['image4']),

                    'image5' => $this->data['image5'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image5'])
                        : $this->replaceUrls($this->data['image5']),

                    'image6' => $this->data['image6'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image6'])
                        : $this->replaceUrls($this->data['image6']),

                    'image7' => $this->data['image7'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image7'])
                        : $this->replaceUrls($this->data['image7']),

                    'gram' => $this->data['gram'],
                    'gold18k' => $this->data['gold18k'],

                    'fullname_agent' => $this->data['fullname_agent'],
                    'phone_agent' => $this->data['phone_agent'],

                ]);

            $count = StatusHistory::where('sefaresh_id', $this->order->id)->where('last_status', $this->data['last_status'])->count();
            if ($count == 0) {

                $historys = StatusHistory::Create([
                    'user_id' => auth()->user()->id,
                    'sefaresh_id' => $this->order->id,
                    'last_status' => $this->data['last_status'],
                    'date_last_status' => $this->data['date_last_status'],
                ]);

                $o = Sefaresh::whereId($this->order->id)->first();
                if ($this->data['last_status'] == 'money') {
                    // smsTasviehMoshtari($o->code, $this->phone, $this->fullname, status_last($this->last_status));
                } elseif ($this->data['last_status'] == 'send' && $this->data['post_type'] == 'پیک') {
                    // smsErsalShodpPeyk($o->code, $this->phone);
                } else {
                    // smsStatusMoshtari($o->code, $this->phone, $this->fullname, status_last($this->last_status));
                }

                StatusSms::create([
                    'user_id' => auth()->user()->id,
                    'code' => $o->code,
                    'phone' => $this->data['phone'],
                    'fullname' => $this->data['fullname'],
                    'status' => $this->data['last_status'],
                ]);

            }

            if ($this->order->invoice_id != null) {
                $invoice = Invoice::where('id', $this->order->invoice_id)->first();
                if ($this->data['last_status'] == 'cancel' && isset($invoice) && $invoice != null && $invoice->status != 'reject') {

                    // dd($invoice->toArray());

                    Invoice::where('id', $this->order->invoice_id)->update([
                        'status' => 'reject',
                    ]);

                    $invoice = Invoice::whereId($this->order->invoice_id)->first();
                    $invoiceDetails = InvoiceProductDetail::where('invoice_id', $invoice->id)->get();

                    foreach ($invoiceDetails as $item) {

                        // if ($item?->product?->product?->fixed_amount != null) {

                        //     Product::whereId($item->product->product->id)->update([
                        //         'out_stock' => $item->product->product->out_stock - $item->count,
                        //         'reserved_count' => $item->product->product->reserved_count - $item->count,
                        //     ]);

                        // } else {

                        // $p = ProductDetail::where('product_id', $item->product->product->id)->where('eticket', $item->eticket)->first();
                        // dd($item->product->product->id);

                        ProductDetail::where('product_id', $item->product->product->id)->where('eticket', $item->eticket)->update([
                            'out_stock' => null,
                            'reserved_count' => null,
                        ]);
                        // }

                    }
                    Artisan::call('optimize:clear');
                }

            }

            $count = StatusHistory::where('sefaresh_id', $this->order->id)->where('last_status', $this->order->last_status)->count();
            if ($count == 0) {

                $historys = StatusHistory::Create([
                    'user_id' => auth()->user()->id,
                    'sefaresh_id' => $this->order->id,
                    'last_status' => $this->order->last_status,
                    'date_last_status' => $this->order->date_last_status,
                ]);

                // $o = Sefaresh::whereId($this->order->id)->first();
                // if ($this->order->last_status == 'money') {
                //     smsTasviehMoshtari($o->code, $this->order->phone, $this->order->fullname, status_last($this->order->last_status));
                // } elseif ($this->order->last_status == 'send' && $$this->order->post_type == 'پیک') {
                //     smsErsalShodpPeyk($o->code, $this->order->phone);
                // } else {
                //     smsStatusMoshtari($o->code, $this->order->phone, $this->order->fullname, status_last($this->order->last_status));
                // }

                StatusSms::create([
                    'user_id' => auth()->user()->id,
                    'code' => $o->code,
                    'phone' => $this->data['phone'],
                    'fullname' => $this->order->fullname,
                    'status' => $this->order->last_status,
                ]);

            }

            if (isset($this->order->tracking_code)) {
                if (auth()->user()->level == 'admin') {
                    $tracking = Tracking::Where('tracking_code', $this->order->tracking_code)->count();
                    if ($tracking == 0) {

                        $t = new Tracking;
                        $t->user_id = auth()->user()->id;
                        $t->sefaresh_id = $this->order->id;
                        $t->fullname = $this->order->fullname;
                        $t->phone = $this->order->phone;
                        $t->tracking_code = $this->order->tracking_code;
                        $t->status = 1;
                        $t->save();

                        if ($this->order->post_type == 'تیباکس') {
                            smsTrackingTipax($this->order->tracking_code, $this->order->phone);
                        } elseif ($this->order->post_type == 'پست' || $this->order->post_type == 'پست ویژه') {
                            smsTrackingPost($this->order->tracking_code, $this->order->phone);
                        }

                    }
                }
            }

            if ($this->data['chResiver'] != null) {
                $resiver = Resiver::updateOrCreate(
                    ['sefaresh_id' => $this->order->id],
                    [
                        'user_id' => auth()->user()->id,
                        'sefaresh_id' => $this->order->id,
                        'fullname' => $this->data['r_fullname'],
                        'codepost' => $this->data['r_codepost'],
                        'mobile' => $this->data['phone'],
                        'address' => $this->data['r_address'],
                    ]
                );
            } else {
                $r = Resiver::where('sefaresh_id', $this->order->id)->first();
                if (isset($r)) {
                    $r->delete();
                }
            }

            DB::commit();

            $this->alert('success', 'بروزرسانی موفق', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => 'ثبت اطلاعات با موفقیت بروزرسانی',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            // $this->alert('success', 'ثبت اطلاعات با موفقیت بروزرسانی', [
            //     'position' => 'top-start',
            // ]);

            // return redirect()->route('admin-dashboard-show-order', $this->order->id);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function replaceUrls($url)
    {
        $domains = [
            'https://tidamode.iran.liara.run/',
            'https://tdservice.ir/',
            'https://shop.tidamode.ir/',
        ];

        foreach ($domains as $domain) {
            if (strpos($url, $domain) === 0) { // اگر ابتدای رشته با این دامین شروع شد
                return substr($url, strlen($domain)); // دامین رو حذف کن
            }
        }

        return $url; // اگر هیچکدوم نبود، همون url اولیه رو برگردون
    }

    public function uploadDataImage($image, $folder)
    {
        if ($image) {
            $file = $image;
            $filename = Str::random(40).'.'.$file->getClientOriginalExtension();
            $file->move('Receipt//'.$folder, $filename);

            return $ImagePatch1 = 'Receipt//'.$folder.'//'.$filename;
        }
    }

    public function updatedDataImage1()
    {

        // $v = verta();
        // $folder = 'userid_'.auth()->user()->id.'_'.$v->year.'-'.$v->month.'-'.$v->day;
        // File::makeDirectory('Receipt\\'.$folder, $mode = 0777, true, true);
        // $filename = Str::random(40).'.'.$this->image1->getClientOriginalExtension();
        // $this->image1->storeAs(path: 'Receipt', name: $filename);
        // $this->image1->storeAs('/', $filename, disk: 'public');
    }

    public function updatedDataTotalAmount()
    {
        $this->calc();
    }

    public function updatedDataDeposit1()
    {
        $this->calc();
    }

    public function updatedDataDeposit2()
    {
        $this->calc();
    }

    public function remove($item)
    {
        if (array_key_exists($item, $this->data)) {
            $this->data[$item] = null;
        }
    }

    private function calc()
    {

        if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
            $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
            $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;
            $total = ((float) $deposit1 + (float) $deposit2);
            $this->data['total'] = number_format($total);
        }

        if ($this->data['total_amount'] != null) {
            if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
                $total = str_replace(',', '', $this->data['total_amount']);
                $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
                $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;

                $remaining = (float) $total - ((float) $deposit1 + (float) $deposit2);
                $this->data['remaining'] = number_format($remaining);
            } else {
                $this->data['remaining'] = $this->total_amount;
            }
        }

        $this->data['deposit1'] = formatMoney($this->data['deposit1']);
        $this->data['deposit2'] = formatMoney($this->data['deposit2']);
        $this->data['remaining'] = formatMoney($this->data['remaining']);
        $this->data['total_amount'] = formatMoney($this->data['total_amount']);
    }

    public $total;

    public $countWeight;

    public $sender;

    public $packing;

    public $commission;

    private function calcFactor()
    {

        // $order = Sefaresh::whereId($this->order->id)->firstorfail();
        // if ($order->last_status != 'cancel') {
        //     $this->total = formatMoney($order->total_amount);
        //     $this->total = 0;
        //     $this->countWeight = isset($order->factors->countWeight) && $order->factors->countWeight != null ? $order->factors->countWeight : 0;

        //     if (isset($order->factors->countWeight) && $order->factors->countWeight != null) {
        //         $weightSum = DB::table('factor_item_empties')
        //             ->where('order_id', $this->order->id)
        //             ->sum(DB::raw('CAST(weight AS FLOAT)'));

        //         $results = DB::table('factor_item_empties')
        //             ->select(
        //                 DB::raw('SUM(CAST(REPLACE(gold18k, \',\', \'\') AS FLOAT) * CAST(weight AS FLOAT) * 0.01) as total_commission'),
        //                 DB::raw('SUM(CAST(REPLACE(sender, \',\', \'\') AS FLOAT)) as total_sender'),
        //                 DB::raw('SUM(CAST(REPLACE(packing, \',\', \'\') AS FLOAT)) as total_packing'),
        //                 DB::raw('SUM(CAST(REPLACE(gold18k, \',\', \'\') AS FLOAT) * CAST(weight AS FLOAT) * (profit / 100)) as total')
        //             )
        //             ->where('order_id', $this->order->id)
        //             ->first();
        //         if ($weightSum < 0.8) {
        //             $totalCommission = 300000;
        //         } elseif ($weightSum >= 0.8 && $weightSum <= 1.2) {
        //             $totalCommission = 400000;
        //         } else {

        //             $totalCommission = $results->total_commission;

        //         }

        //         // dd($this->total);
        //         $this->total = formatMoney($results->total / 10);

        //         $this->sender = formatMoney($results->total_sender);
        //         $this->packing = formatMoney($results->total_packing);

        //         $this->commission = formatMoney($totalCommission);

        //         $financial = Financial::where('sefaresh_id', $this->order->id)->first();
        //         if (isset($financial) && $financial != null) {
        //             $financial->commission = $this->commission;
        //             $financial->sender = $this->sender;
        //             $financial->packing = $this->packing;
        //             $financial->profit = str_replace(',', '', $this->total) * 10;
        //             $financial->save();
        //         }

        //     }

        // } else {
        //     $financial = Financial::where('sefaresh_id', $this->order->id)->first();
        //     if (isset($financial) && $financial != null) {
        //         $financial->commission = 0;
        //         $financial->profit = 0;
        //         $financial->save();
        //     }
        // }

    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        $user = Subscribe::whereId($userId)->first();
        if (isset($user) && $user != null) {
            $this->data['fullname'] = $user->fullname;
            $this->data['phone'] = $user->mobile;
            $this->data['code'] = $user->code;
            $this->data['address'] = $user->address;
            $this->data['codepost'] = $user->codepost;
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.order.show', [
            'users' => \App\Models\User::where('status', '1')->get(),
        ]);
    }
}
