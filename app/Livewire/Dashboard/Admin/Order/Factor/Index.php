<?php

namespace App\Livewire\Dashboard\Admin\Order\Factor;

use App\Models\FactorItem;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Sefaresh;
use App\Models\Subscribe;
use <PERSON>tinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Lazy;
use Livewire\Attributes\On;
use Livewire\Component;

class Index extends Component
{
    use LivewireAlert;

    public $order;

    public $items;

    public array $data = [
        'post' => null,
        // 'order' => null,
        'title' => null,
        'code' => null,
        'eticket' => null,
        'phone' => null,
        'gold18k' => null,
        'factor_number' => null,
        'factor_create_date' => null,
        'factor_total' => null,
        'factor_deposit' => null,
        'factor_discount' => null,
        'total' => null,
        'user_attach' => null,
        'fullnameR' => null,
        'phoneR' => null,
        'gift' => false,
    ];

    public function mount()
    {

        // dd($this->order);

        $this->data['gold18k'] = formatMoney($this->order?->gold18k) ?? getGold18k();
        // dd($this->data['gold18k']);
        if ($this->order == null) {

            $this->loadFactorEmpty();
            $this->data['gold18k'] = getGold18k();

            return;
        }

        $this->loadFactorOrder();

        // $this->loadGold18k();
    }

    public function updatedDataGold18k()
    {
        $this->data['gold18k'] = str_replace(',', '', $this->data['gold18k']);
        $this->data['gold18k'] = number_format($this->data['gold18k']);
        if ($this->order == null) {
            FactorItemEmpty::where('order_id', null)->update([
                'gold18k' => $this->data['gold18k'],
            ]);
        } else {
            FactorItemEmpty::where('order_id', $this->order->id)->update([
                'gold18k' => $this->data['gold18k'],
            ]);

            Sefaresh::whereId($this->order->id)->update([
                'gold18k' => $this->data['gold18k'],
            ]);

        }
        $this->dispatch('gold18k', gold18k: $this->data['gold18k']);
    }

    private function loadFactorEmpty()
    {

        $factorItems = FactorItemEmpty::where('order_id', null)->get();
        if ($factorItems->count() == 0) {

            $v = verta();
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;
            $today = $v->year.'/'.$month.'/'.$day;

            $factorNumberId = FactorOrderEmpty::latest()->first();
            $setting = \App\Models\Setting::where('type', 'factorId')->first();
            $factorNumber = isset($setting) && $setting != null ? $setting->body : 1;
            $factor = FactorOrderEmpty::create([
                'user_id' => auth()->user()->id,
                'factor_number' => isset($factorNumberId) && $factorNumberId != null ? intval($factorNumberId->factor_number) + 1 : $factorNumber,
                'factor_create_date' => $today,
            ]);

            for ($i = 0; $i < 5; $i++) {
                FactorItemEmpty::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $factor->factor_number,
                    'code' => '',
                    'eticket' => '',
                    'title' => '',
                    'gold18k' => '',
                    'cutie' => '18',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '7',
                    'tax' => '10',
                    'total' => '0',
                ]);

            }
        }

        $factorOrderEmpty = FactorOrderEmpty::where('order_id', null)->latest()->first();
        if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {
            $this->data['title'] = $factorOrderEmpty->fullname;
            $this->data['phone'] = $factorOrderEmpty->phone;
            $this->data['factor_number'] = $factorOrderEmpty->factor_number;
            $this->data['factor_create_date'] = $factorOrderEmpty->factor_create_date;
            $this->data['code'] = $factorOrderEmpty->subscribe_code;
            $this->data['eticket'] = $factorOrderEmpty->eticket;
            $this->data['factor_total'] = $factorOrderEmpty->factor_total;
            $this->data['factor_deposit'] = $factorOrderEmpty->factor_deposit;
            $this->data['factor_discount'] = $factorOrderEmpty->factor_discount;
            $this->data['total'] = $factorOrderEmpty->total;
            $this->data['user_attach'] = $factorOrderEmpty->user_attach;
            $this->data['fullnameR'] = $factorOrderEmpty->fullname_r;
            $this->data['phoneR'] = $factorOrderEmpty->phone_r;
            $this->data['gift'] = $factorOrderEmpty->gift == 1 ? true : false;

        }

        $factorItems = FactorItemEmpty::where('order_id', null)->get();
        $this->items = $factorItems;
    }

    private function loadFactorOrder()
    {
        // dd($this->order->id);

        $deposit1 = $this->order->deposit1 != null ? str_replace(',', '', $this->order->deposit1) : 0;
        $deposit2 = $this->order->deposit2 != null ? str_replace(',', '', $this->order->deposit2) : 0;

        // if(isset($factorLatestId) &&  $factorLatestId != null){

        //     $factorItems = FactorItem::where('order_id', $this->order->id)->get();
        //     $this->items = $factorItems;
        // }else{
        $factorItems = FactorItemEmpty::where('order_id', $this->order->id)->get();
        if ($factorItems->count() == 0) {

            $v = verta();
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;
            $today = $v->year.'/'.$month.'/'.$day;

            $factorNumberId = FactorOrderEmpty::latest()->first();
            $setting = \App\Models\Setting::where('type', 'factorId')->first();
            $factorNumber = isset($setting) && $setting != null ? $setting->body : 1;
            $factor = FactorOrderEmpty::create([
                'user_id' => auth()->user()->id,
                'order_id' => $this->order->id,
                'factor_deposit' => $deposit1 + $deposit2,
                'factor_discount' => '0',
                'factor_number' => isset($factorNumberId) && $factorNumberId != null ? intval($factorNumberId->factor_number) + 1 : $factorNumber,
                'factor_create_date' => shamsiDateLimit($this->order->created_at),
            ]);

            FactorItemEmpty::create([
                'user_id' => auth()->user()->id,
                'factor_id' => $factor->factor_number,
                'order_id' => $this->order->id,
                'code' => $this->order->code,
                'eticket' => '',
                'title' => $this->order->name_pluck,
                // 'gold18k' => '',
                'cutie' => '18',
                'weight' => $this->order->gram != null ? $this->order->gram : 0,
                'construction_wages' => '0',
                'profit' => '7',
                'tax' => '10',
                'total' => '0',
            ]);

            for ($i = 0; $i < 5; $i++) {
                FactorItemEmpty::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $factor->factor_number,
                    'order_id' => $this->order->id,
                    'code' => '',
                    'title' => '',
                    // 'gold18k' => '',
                    'cutie' => '18',
                    'weight' => '0',
                    'construction_wages' => '0',
                    'profit' => '7',
                    'tax' => '10',
                    'total' => '0',
                ]);

            }
        }

        $factorOrderEmpty = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
        if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {

            $factorOrderEmpty->fullname = $this->order->fullname;
            $factorOrderEmpty->phone = $this->order->phone;
            $user = Subscribe::where('mobile', $this->order->phone)->first();
            if (isset($user) && $user != null) {
                $factorOrderEmpty->subscribe_code = $user->code;
            }
            $factorOrderEmpty->save();

            $factorOrderEmpty = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            $this->data['title'] = $factorOrderEmpty->fullname;
            $this->data['phone'] = $factorOrderEmpty->phone;
            $this->data['factor_number'] = $factorOrderEmpty->factor_number;
            $this->data['factor_create_date'] = $factorOrderEmpty->factor_create_date;
            $this->data['code'] = $factorOrderEmpty->subscribe_code;
            $this->data['eticket'] = $factorOrderEmpty->eticket;
            $this->data['factor_total'] = $factorOrderEmpty->factor_total;
            $this->data['factor_deposit'] = $factorOrderEmpty->factor_deposit;
            $this->data['factor_discount'] = $factorOrderEmpty->factor_discount;
            $this->data['total'] = $factorOrderEmpty->total;
            $this->data['user_attach'] = $factorOrderEmpty->user_attach;
            $this->data['fullnameR'] = $factorOrderEmpty->fullname_r;
            $this->data['phoneR'] = $factorOrderEmpty->phone_r;
            $this->data['gift'] = $factorOrderEmpty->gift == 1 ? true : false;

        }

        // $factorItems = FactorItemEmpty::where('order_id', $this->order->id)->get();
        // if (isset($factorItems) && $factorItems != null) {
        //     $gold18k = $factorItems->first();
        //     // $this->data['gold18k'] = $gold18k->gold18k;
        // }

        $this->items = $factorItems;
        // }

    }

    #[Lazy]
    public function loadGold18k()
    {
        $this->data['gold18k'] = getGold18k();
        // $this->gold18k = str_replace(',', '', $this->gold18k);
        // $this->gold18k = number_format($this->gold18k * 10);
        if ($this->order == null) {
            FactorItemEmpty::where('order_id', null)->update([
                'gold18k' => $this->data['gold18k'],
            ]);
        } else {
            FactorItemEmpty::where('order_id', $this->order->id)->update([
                'gold18k' => $this->data['gold18k'],
            ]);

            Sefaresh::whereId($this->order->id)->update([
                'gold18k' => $this->data['gold18k'],
            ]);
        }
        $this->dispatch('gold18k', gold18k: $this->data['gold18k']);
    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        if ($this->order == null) {
            $user = Subscribe::whereId($userId)->first();

            if (isset($user) && $user != null) {
                $this->data['title'] = $user->fullname;
                $this->data['phone'] = $user->mobile;
                $this->data['code'] = $user->code;
            }

            $factorOrderEmpty = FactorOrderEmpty::where('order_id', null)->latest()->first();
            if (isset($factorOrderEmpty) && $factorOrderEmpty != null) {

                $factorOrderEmpty->fullname = $user->fullname;
                $factorOrderEmpty->phone = $user->mobile;
                $factorOrderEmpty->subscribe_code = $user->code;
                $factorOrderEmpty->subscribe_id = $user->id;
                $factorOrderEmpty->fullname_r = $this->data['fullnameR'];
                $factorOrderEmpty->phone_r = $this->data['phoneR'];
                $factorOrderEmpty->gift == 1 ? true : false;
                $factorOrderEmpty->save();
            }
        } else {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'امکان ویرایش اطلاعات مشتری از این بخش امکان پذیر نیست، بایستی در فرم ویرایش این اطلاعات اصلاح گردد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }

    }

    public function updatedDataFullnameR()
    {

        $this->saveRFullname();
    }

    public function updatedDataPhoneR()
    {
        $this->saveRFullname();
    }

    public function updatedDataGift()
    {
        $this->saveRFullname();
    }

    private function saveRFullname()
    {

        if ($this->order != null) {
            $factorOrderEmpty = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            $factorOrderEmpty->fullname_r = $this->data['fullnameR'];
            $factorOrderEmpty->phone_r = $this->data['phoneR'];
            $factorOrderEmpty->gift = $this->data['gift'];
            $factorOrderEmpty->save();
        }
    }

    public function acceptGold()
    {
        if ($this->order == null) {
            FactorItemEmpty::where('order_id', null)->update([
                'gold18k' => $this->data['gold18k'],
            ]);
        } else {
            FactorItemEmpty::where('order_id', $this->order->id)->update([
                'gold18k' => $this->data['gold18k'],
            ]);
        }
        $this->dispatch('gold18k', gold18k: $this->data['gold18k']);
    }

    public function render()
    {

        return view('livewire.dashboard.admin.order.factor.index');
    }
}
