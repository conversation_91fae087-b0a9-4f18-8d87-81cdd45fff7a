<?php

namespace App\Livewire\Dashboard\Admin\Order\Factor;

use App\Models\FactorItem;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrder;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\Subscribe;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class Sum extends Component
{
    use LivewireAlert;

    public $order;

    public $total;

    public $totalFactor;

    public $deposit;

    public $discount;

    public $countWeight = 0;

    public $post = null;

    public $post_type;

    public $status = 'yes';

    public $userRefer;

    public $sex = 'طلا';

    public $category;

    public $last_status;

    #[On('change-total-factor')]
    public function loadFactor()
    {
        $this->calcFactor();
    }

    public function mount()
    {
        $this->calcFactor();
    }

    public $orderId;

    public function saveFactor()
    {

        // dd($this->status, $this->userRefer, $this->sex, $this->category);
        // $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        // $subscribe = Subscribe::where('mobile', $factor->phone)->latest()->first();

        // dd($factor, $subscribe);

        $user = $this->userRefer == null ? auth()->user()->id : $this->userRefer;
        // if($this->status == 'yes'){
        // }

        $this->createOrder($user);

    }

    private function createOrder($user)
    {

        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factorItem = FactorItemEmpty::where('order_id', null)->latest()->first();
        $subscribe = Subscribe::where('mobile', $factor->phone)->latest()->first();

        // dd($factor, $subscribe);

        $v = verta();
        $code = '';

        $c = Sefaresh::where('month', '=', $v->month)->where('year', '=', $v->year)->count();
        if (isset($c)) {
            $c += 1;
            $this->sex == 'طلا' ? $code = 'G'.substr($v->year, 3).$v->month.'/'.$c : $code = 'S'.substr($v->year, 3).$v->month.'/'.$c;
        }

        try {

            DB::beginTransaction();
            $order = Sefaresh::create(
                [
                    'code' => $code,
                    'chat_id' => Str::uuid(),
                    'user_id' => $user,
                    'type' => $this->category,
                    'type_construction' => 'سفارشی',
                    'model' => $factorItem->title,
                    'sex' => $this->sex,
                    'color' => '-',
                    'font' => '-',
                    'name_pluck' => '-',
                    'post_type' => $this->post_type,
                    'tracking_code' => '-',
                    'tips' => '-',
                    'total_amount' => str_replace(',', '', $this->total),
                    'deposit1' => str_replace(',', '', $this->deposit),
                    'deposit2' => '0',
                    'remaining' => str_replace(',', '', $this->totalFactor),
                    'sefaresh_total' => str_replace(',', '', $this->total),
                    'package_type' => '-',
                    'package_amount' => '-',

                    'fullname' => $factor->fullname,
                    'phone' => $factor->phone,
                    'address' => isset($subscribe->address) && $subscribe->address != null ? $subscribe->address : '',
                    'codepost' => isset($subscribe->codepost) && $subscribe->codepost != null ? $subscribe->codepost : '',

                    'chain' => '-',
                    'size' => '-',
                    'size_wrist' => '-',
                    'size_ankle' => '-',

                    'order_register_date' => $factor->factor_create_date,
                    'customer_date' => $factor->factor_create_date,
                    'last_status' => $this->last_status,
                    'date_last_status' => '-',

                    'whatsapp' => '',
                    'phone_whatsapp' => '0',
                    'product_code' => '0',

                    'designer_id' => '',
                    'manufacturer_id' => '',
                    'dimensions' => '',

                    'year' => $v->year,
                    'month' => $v->month,
                    'day' => $v->day,

                    'gram' => $factor->countWeight,
                    'gold18k' => $factorItem->gold18k,

                ]);

            $financials = Financial::updateOrCreate(
                ['sefaresh_id' => $order->id],
                [
                    'user_id' => $user,
                    'sefaresh_id' => $order->id,
                ]
            );

            $historys = StatusHistory::Create([
                'user_id' => $user,
                'sefaresh_id' => $order->id,
                'last_status' => $factor->factor_create_date,
                'date_last_status' => $this->last_status,
            ]);

            $this->orderId = $order->id;
            DB::commit();
            $this->createFactor($user);

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'ثبت سفارش با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    private function createFactor($user)
    {
        try {

            DB::beginTransaction();

            $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
            $factor->order_id = $this->orderId;
            $factor->save();
            $factorCreate = FactorOrder::create([
                'user_id' => auth()->user()->id,
                'order_id' => $this->orderId,
                'subscribe_id' => $factor->subscribe_id,
                'fullname' => $factor->fullname,
                'subscribe_code' => $factor->subscribe_code,
                'phone' => $factor->phone,
                'factor_total' => $factor->factor_total,
                'total' => $factor->total,
                'countWeight' => $factor->countWeight,
                'post' => $factor->post,
                'user_attach' => $user,
                'factor_deposit' => $factor->factor_deposit,
                'factor_discount' => $factor->factor_discount,
                'factor_number' => $factor->factor_number,
                'factor_create_date' => $factor->factor_create_date,
                'fullname_r' => $factor->fullname_r,
                'phone_r' => $factor->phone_r,
                'gift' => $factor->gift,
            ]);

            $factorItems = FactorItemEmpty::where('order_id', null)->get();
            foreach ($factorItems as $item) {

                $item->order_id = $this->orderId;
                $item->save();

                FactorItem::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $factorCreate->id,
                    'order_id' => $this->orderId,
                    'code' => $item->code,
                    'eticket' => $item->eticket,
                    'title' => $item->title,
                    'gold18k' => $item->gold18k,
                    'cutie' => $item->cutie,
                    'weight' => $item->weight,
                    'construction_wages' => $item->construction_wages,
                    'profit' => $item->profit,
                    'total' => $item->total,
                ]);
                // $item->delete();
            }

            // $factor->delete();

            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            if ($this->orderId != null) {
                return redirect()->route('admin-dashboard-show-order', $this->orderId);
            }

            return redirect()->route('admin-dashboard-factors');

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'ثبت فاکتور با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    // public function updatedPost(){

    //     if(isset($this->order) && $this->order->id != null){

    //         $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
    //         $factor->post = $this->post;
    //         $factor->save();

    //         if($this->post == 'yes'){
    //             $item = FactorItemEmpty::where('order_id', $this->order->id)->latest()->first();
    //             $item->total = '100,000';
    //             $item->save();
    //         }

    //     }else{
    //         if($this->post == 'yes'){

    //             $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
    //             $factor->post = $this->post;
    //             $factor->save();

    //             if($this->post == 'yes'){
    //                 $item = FactorItemEmpty::where('order_id', null)->latest()->first();
    //                 $item->total = '100,000';
    //                 $item->save();
    //             }

    //         }
    //     }

    //     $this->dispatch('post', post: $this->post);

    // }

    private function calcFactor()
    {
        if (isset($this->order) && $this->order->id != null) {
            $this->loadSumFactorOrder();
        } else {
            $this->loadSumFactorEmpty();
        }
        $this->dispatch('change-factor');
    }

    private function loadSumFactorOrder()
    {
        $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
        if (isset($factor) && $factor != null) {
            // dd($factor);
            // استفاده از Query Builder برای محاسبه مجموع total در جدول FactorItemEmpty
            $total = FactorItemEmpty::where('order_id', $this->order->id)->sum(DB::raw('REPLACE(total, ",", "")'));
            $countWeight = FactorItemEmpty::where('order_id', $this->order->id)->sum(DB::raw('REPLACE(weight, ",", "")'));
            // به‌روزرسانی مقدار factor_total برای رکورد factor
            $factor->factor_total = number_format($total, 0, '.', ',');

            $factor_total = $total;
            // $deposit = str_replace(',', '',$factor->factor_deposit);
            $discount = $factor->factor_discount != null ? $factor->factor_discount : 0;
            $deposit = $factor->factor_deposit != null ? $factor->factor_deposit : 0;
            $totalFactor = $factor_total - $deposit - $discount;
            $factor->total = number_format($totalFactor, 0, '.', ',');

            $factor->countWeight = $countWeight;

            // $factor->post = $this->post;

            $factor->save(); // ذخیره‌سازی تغییرات در پایگاه داده
        }

        $this->total = $factor->factor_total;

        $this->deposit = $factor->factor_deposit != null ? number_format($factor->factor_deposit) : 0;

        $this->discount = $factor->factor_discount != null ? number_format($factor->factor_discount) : 0;

        $this->totalFactor = $factor->total;

        $this->countWeight = (int) $factor->countWeight;

        // $this->post = $factor->post;
    }

    private function loadSumFactorEmpty()
    {
        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        if (isset($factor) && $factor != null) {
            // استفاده از Query Builder برای محاسبه مجموع total در جدول FactorItemEmpty
            $total = FactorItemEmpty::where('order_id', null)->sum(DB::raw('REPLACE(total, ",", "")'));
            $countWeight = FactorItemEmpty::where('order_id', null)->sum(DB::raw('REPLACE(weight, ",", "")'));

            // به‌روزرسانی مقدار factor_total برای رکورد factor
            $factor->factor_total = number_format($total, 0, '.', ',');

            $factor_total = $total;
            // $deposit = number_format($factor->factor_deposit, 0, '.', ',');
            $discount = $factor->factor_discount != null ? $factor->factor_discount : 0;
            $deposit = $factor->factor_deposit != null ? $factor->factor_deposit : 0;
            $totalFactor = $factor_total - $deposit - $discount;
            $factor->total = number_format($totalFactor, 0, '.', ',');

            $factor->countWeight = $countWeight;
            // $factor->post = $this->post;

            $factor->save(); // ذخیره‌سازی تغییرات در پایگاه داده
        }

        $this->total = $factor->factor_total;

        $this->deposit = $factor->factor_deposit != null ? number_format($factor->factor_deposit) : 0;

        $this->discount = $factor->factor_discount != null ? number_format($factor->factor_discount) : 0;

        $this->totalFactor = $factor->total;

        $this->countWeight = $factor->countWeight;

        // $this->post = $factor->post;

    }

    public function addItems()
    {

        $factorLatestId = FactorOrderEmpty::latest()->first();

        $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
        if (! isset($factor)) {

            $deposit1_factor = str_replace(',', '', $this->order->deposit1);
            $deposit2_factor = str_replace(',', '', $this->order->deposit2);
            $deposit_factor = (float) $deposit1_factor + $deposit2_factor;

            $factor = FactorOrderEmpty::create([
                'user_id' => $this->order->user_id,
                'order_id' => $this->order->id,
                'fullname' => $this->order->fullname,
                'phone' => $this->order->phone,
                'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
                'factor_create_date' => $this->order->order_register_date,
                'subscribe_code' => null,
                'factor_total' => $this->order->total,
                'factor_deposit' => 0,
                'factor_discount' => $deposit_factor,
                'total' => $this->order->total,
                'countWeight' => $this->order->gram ?? 0,
                'post' => null,
                'gold18kup' => $this->order->gold18k,
            ]);

            for ($i = 1; $i <= 5; $i++) {
                FactorItemEmpty::create([
                    'user_id' => $this->order->user_id,
                    'order_id' => $this->order->id,
                    'factor_id' => $factor->id,
                    'subscribe_id' => null,
                    'code' => null,
                    'eticket' => null,
                    'title' => null,
                    'gold18k' => null,
                    'cutie' => null,
                    'weight' => null,
                    'construction_wages' => null,
                    'profit' => null,
                    'tax' => null,
                    'total' => null,
                ]);
            }

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            return redirect()->route('admin-dashboard-show-order', $this->order->id);

        }

        // $this->alert('error', 'خطا در ایجاد در ردیف', [
        //     'position' => 'center',
        //     'timer' => 3000,
        //     'toast' => false,
        //     'text' => 'طبق محاسبه این فاکتور شامل 5 ردیف می باشد و بیشتر از این نمی توان ایجاد کرد',
        //     'timerProgressBar' => true,
        //     'showDenyButton' => true,
        //     'onDenied' => '',
        //     'denyButtonText' => 'بسیار خب متوجه شدم',
        // ]);
        $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
            'position' => 'top-start',
        ]);

        return redirect()->route('admin-dashboard-show-order', $this->order->id);
    }

    public function updatedDeposit()
    {
        $deposit = str_replace(',', '', $this->deposit);

        if (isset($this->order->id) && $this->order->id != null) {
            $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            $factor->factor_deposit = $deposit != null ? $deposit : '0';
            $factor->save();
        } else {
            $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
            $factor->factor_deposit = $deposit != null ? $deposit : '0';
            $factor->save();
        }

        $this->calcFactor();
    }

    public function updatedDiscount()
    {
        $discount = str_replace(',', '', $this->discount);
        if (isset($this->order->id) && $this->order->id != null) {
            $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            $factor->factor_discount = $discount != null ? $discount : '0';
            $factor->save();
        } else {
            $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
            $factor->factor_discount = $discount != null ? $discount : '0';
            $factor->save();
        }

        $this->calcFactor();
    }

    public function render()
    {
        return view('livewire.dashboard.admin.order.factor.sum', [
            'users' => \App\Models\User::where('status', '1')->get(),
        ]);
    }
}
