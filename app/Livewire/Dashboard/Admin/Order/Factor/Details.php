<?php

namespace App\Livewire\Dashboard\Admin\Order\Factor;

use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use Livewire\Attributes\On;
use Livewire\Component;

class Details extends Component
{
    public $image1;

    public $order;

    public $total = 0;

    public $commission = 0;

    public $countWeight = 0;

    public $sender = 0;

    public $packing = 0;

    public $image_server_url;

    // public function mount(){
    //     $this->total = formatMoney($this->order->total_amount);
    //     $this->countWeight = isset($this->order->factors->countWeight) && $this->order->factors->countWeight != null ? $this->order->factors->countWeight : 0;
    // }

    public function mount()
    {
        $image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        $image_server_url_2 = env('APP_ASSET_URL');

        // تعیین مسیر تصویر اول در سرور اول
        $image1_path_1 = $image_server_url_1.$this->order->image1;

        // بررسی وجود تصویر اول در مسیر اول
        $headers = @get_headers($image1_path_1);
        if ($headers && strpos($headers[0], '200')) {
            // تصویر در مسیر اول وجود دارد، استفاده از مسیر اول برای تمامی تصاویر
            $this->image_server_url = $image_server_url_1;
        } else {
            // تصویر در مسیر اول وجود ندارد، استفاده از مسیر دوم برای تمامی تصاویر
            $this->image_server_url = $image_server_url_2;
        }

        $headers = @get_headers($this->image_server_url.$this->order->image1);
        if ($headers && strpos($headers[0], '200')) {
            // تنظیم مسیر نهایی برای تصاویر
            $this->image1 = $this->order->image1 != null ? $this->image_server_url.$this->order->image1 : '';
        } else {
            // تنظیم مسیر نهایی برای تصاویر
            $this->image1 = $this->order->image1 != null ? 'https://shop.tidamode.ir/'.$this->order->image1 : '';
        }

    }

    #[On('change-factor')]
    public function loadFactor()
    {
        $this->calcFactor();
    }

    private function calcFactor()
    {

        try {
            $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            if ($factor) {
                $weight = FactorItemEmpty::where('order_id', $this->order->id)
                    ->whereNotNull('weight')
                    ->sum('weight');

                $this->commission = calcCommission($weight, $factor->order->gold18k);
                $this->total = $weight * (float) str_replace(',', '', $factor->order->gold18k) * 7 / 10;

                Financial::updateOrCreate(
                    ['sefaresh_id' => $this->order->id],
                    [
                        'user_id' => auth()->user()->id,
                        'sefaresh_id' => $this->order->id,
                        'commission' => (float) str_replace(',', '', $this->commission),
                        'profit' => (float) str_replace(',', '', $this->total),
                    ]
                );

                $this->total = formatMoney($this->total);
                $this->commission = formatMoney($this->commission);

            }
        } catch (\Exception $e) {
            // $this->alert('error', 'خطا در ثبت', [
            //     'position' => 'center',
            //     'timer' => 3000,
            //     'toast' => false,
            //     'text' => 'محاسبه هوشمند مالی برای این سفارش با مشکل مواجه شده. دقت بفرمایید اطلاعات فاکتور این سفارش به درستی پر شده باشند',
            //     'timerProgressBar' => true,
            //     'showDenyButton' => true,
            //     'onDenied' => '',
            //     'denyButtonText' => 'بسیار خب متوجه شدم',
            // ]);

        }

    }

    public function render()
    {
        // $this->calcFactor();

        return view('livewire.dashboard.admin.order.factor.details', [
            'factors' => FactorItemEmpty::where('order_id', $this->order->id)->get(),
        ]);
    }
}
