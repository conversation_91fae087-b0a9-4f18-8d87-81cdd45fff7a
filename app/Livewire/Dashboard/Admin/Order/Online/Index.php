<?php

namespace App\Livewire\Dashboard\Admin\Order\Online;

use App\Models\Invoice;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public array $data = [
        'userId' => null,
        'orderId' => null,
        'fullname' => null,
        'phone' => null,
        'status' => null,
        'eticket' => null,
        'productName' => null,
    ];

    public function ClearFillter()
    {
        $this->reset(['data']);
    }

    public function fillter() {}

    public function render()
    {

        $invoices = Invoice::with('user');

        if (auth()->user()->level != 'admin') {
            $invoices->where('user_id', auth()->user()->id);
        }

        if ($this->data['orderId'] != null) {
            $invoices->where('id', hashId($this->data['orderId']));
        }

        if ($this->data['userId'] != null) {
            $invoices->where('user_id', $this->data['userId']);
        }

        if ($this->data['status'] != null) {
            $invoices->where('status', $this->data['status']);
        }

        if ($this->data['fullname'] != null) {
            $invoices->where('fullname', 'like', '%'.$this->data['fullname'].'%');
        }

        if ($this->data['phone'] != null) {
            $invoices->where('phone', 'like', '%'.$this->data['phone'].'%');
        }

        if ($this->data['eticket'] != null) {
            $invoices->whereHas('productDetails', function ($query) {
                $query->where('eticket', $this->data['eticket']);
            });
        }

        if ($this->data['productName'] != null) {
            $invoices->whereHas('products', function ($query) {
                $query->where('title_fa', 'like', '%'.$this->data['productName'].'%');
            });
        }

        // Paginate the results
        $paginatedInvoices = $invoices->latest()->paginate(perPage: 10);

        // Add the countdown attribute to each item in the collection
        $paginatedInvoices->getCollection()->transform(function ($item) {
            $item->countdown = now()->diffInSeconds($item->created_at->addHours(2));

            return $item;
        });

        return view('livewire.dashboard.admin.order.online.index', [
            'invoices' => $paginatedInvoices,
            'users' => User::whereIn('level', ['admin', 'user'])->get(),
        ]);
    }
}
