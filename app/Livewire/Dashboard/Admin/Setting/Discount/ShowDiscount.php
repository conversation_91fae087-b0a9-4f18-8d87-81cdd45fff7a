<?php

namespace App\Livewire\Dashboard\Admin\Setting\Discount;

use App\Models\Discount;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowDiscount extends Component
{
    use LivewireAlert;

    public $discountId;

    public array $discount = [
        'title' => null,
        'code' => null,
        'count' => null,
        'minimumـprice' => null,
        'maximumـprice' => null,
        'percentage' => null,
        'start_date_time' => null,
        'end_date_time' => null,
        'status' => null,
    ];

    #[On('set-discount')]
    public function getDiscount($discountId)
    {
        $this->discountId = $discountId;
        $discount = Discount::whereId($discountId)->firstorfail();
        $this->discount['title'] = $discount->title;
        $this->discount['code'] = $discount->code;
        $this->discount['count'] = $discount->total_uses;
        $this->discount['minimumـprice'] = formatMoney($discount->minimumـprice);
        $this->discount['maximumـprice'] = formatMoney($discount->maximumـprice);
        $this->discount['percentage'] = $discount->percentage;
        $this->discount['start_date_time'] = $discount->start_date;
        $this->discount['end_date_time'] = $discount->expiry_date;
        $this->discount['status'] = $discount->status;

    }

    public function rules()
    {
        return [
            'discount.title' => 'required',
            'discount.code' => 'required',
            'discount.start_date_time' => 'required',
            'discount.end_date_time' => 'required',
            'discount.percentage' => 'required',

        ];
    }

    public function messages()
    {
        return [
            'discount.title.required' => 'پر کردن فیلد الزامیست',
            'discount.code.required' => 'پر کردن فیلد الزامیست',
            'discount.start_date_time.required' => 'پر کردن فیلد الزامیست',
            'discount.end_date_time.required' => 'پر کردن فیلد الزامیست',
            'discount.percentage.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function store()
    {
        $this->validate();

        // $startDateTime = (new Verta($this->discount['start_date_time']))->DateTime();
        // $endDateTime = (new Verta($this->discount['end_date_time']))->DateTime();

        // dd($startDateTime, $endDateTime);
        Discount::whereId($this->discountId)->update([
            'title' => $this->discount['title'],
            'code' => $this->discount['code'],
            'total_uses' => $this->discount['count'],
            'start_date' => $this->discount['start_date_time'],
            'expiry_date' => $this->discount['end_date_time'],
            'minimumـprice' => (float) str_replace(',', '', $this->discount['minimumـprice']),
            'maximumـprice' => (float) str_replace(',', '', $this->discount['maximumـprice']),
            'percentage' => $this->discount['percentage'],
            'status' => $this->discount['status'],
        ]);

        $this->alert('success', 'بروزرسانی با موفقیت انجام شد', [
            'position' => 'top-start',
        ]);

        $this->dispatch('reload-discount-list');

    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.discount.show-discount');
    }
}
