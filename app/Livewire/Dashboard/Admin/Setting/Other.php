<?php

namespace App\Livewire\Dashboard\Admin\Setting;

use App\Jobs\UpdateAmountProductsJob;
use Illuminate\Support\Facades\Artisan;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class Other extends Component
{
    use LivewireAlert;

    public function cacheClear()
    {
        Artisan::call('optimize:clear');
        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);
    }

    public function productsUpdate()
    {
        UpdateAmountProductsJob::dispatch();
        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div class="text-base">
            <span>درحال بارگزاری ...    </span>
        </div>
        HTML;
    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.other');
    }
}
