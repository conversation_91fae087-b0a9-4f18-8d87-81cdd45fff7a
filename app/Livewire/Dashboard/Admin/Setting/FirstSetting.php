<?php

namespace App\Livewire\Dashboard\Admin\Setting;

use App\Models\Setting;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Lazy;
use Livewire\Component;

class FirstSetting extends Component
{
    use LivewireAlert;

    public array $data = [
        'gold18k_status' => true,
        'gold18k' => '',
        'gold18kup' => '',
        'factorId' => '',
        'profit' => '',
        'construction_wages' => '',
        'tax' => '',
        'expireTimeLink' => '',
        'factor' => '',
        'idleTimeout' => '',
        'countdownTime' => '',
        'popupTime' => '',
        'post_price' => '',
    ];

    public function loadGold()
    {

        if ($this->data['gold18k_status'] == true || $this->data['gold18k_status'] == 1) {
            $this->data['gold18k'] = getGold18kOrigin();
        }

        DB::beginTransaction();
        try {

            $types = [
                'gold18k_status',
                'gold18k',
                'gold18kup',
                'factor',
                'expireTimeLink',
                'profit',
                'construction_wages',
                'tax',
                'idleTimeout',
                'countdownTime',
                'popupTime',
                'post_price',
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            return redirect('/setting');
        } catch (\Exception $exceptionxception) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function mount()
    {
        $settings = Setting::whereIn('type', [
            'gold18k',
            'factorId',
            'gold18kup',
            'gold18k_status',
            'factor',
            'profit',
            'construction_wages',
            'tax',
            'expireTimeLink',
            'idleTimeout',
            'countdownTime',
            'popupTime',
            'post_price',
        ])->pluck('body', 'type')->toArray();

        $this->data['gold18k_status'] = $settings['gold18k_status'] == 1 ? true : false;
        $this->data['factor'] = isset($settings['factor']) && $settings['factor'] == 1 ? true : false;

        $this->data['gold18kup'] = formatMoney($settings['gold18kup']) ?? null;
        $this->data['factorId'] = $settings['factorId'] ?? null;
        $this->data['expireTimeLink'] = $settings['expireTimeLink'] ?? null;
        $this->data['construction_wages'] = $settings['construction_wages'] ?? null;
        $this->data['profit'] = $settings['profit'] ?? null;
        $this->data['tax'] = $settings['tax'] ?? null;
        $this->data['idleTimeout'] = $settings['idleTimeout'] ?? null;
        $this->data['countdownTime'] = $settings['countdownTime'] ?? null;
        $this->data['popupTime'] = $settings['popupTime'] ?? null;
        $this->data['post_price'] = formatMoney($settings['post_price']) ?? null;

        $this->loadGold18k($settings['gold18k']);
    }

    #[Lazy]
    public function loadGold18k($gold18k)
    {
        if ($this->data['gold18k_status'] == true) {
            $this->data['gold18k'] = getGold18kOrigin();
        } else {
            $this->data['gold18k'] = formatMoney($gold18k) ?? null;
        }
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div class="text-base">
            <span>درحال بارگزاری ...    </span>
        </div>
        HTML;
    }

    public function save()
    {
        DB::beginTransaction();
        try {

            $types = [
                'gold18k_status',
                'gold18k',
                'gold18kup',
                'factorId',
                'factor',
                'expireTimeLink',
                'profit',
                'construction_wages',
                'tax',
                'idleTimeout',
                'countdownTime',
                'popupTime',
                'post_price',
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();

            Artisan::call('optimize:clear');
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            return redirect('/setting');
        } catch (\Exception $exceptionxception) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.first-setting');
    }
}
