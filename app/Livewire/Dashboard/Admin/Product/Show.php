<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Models\Product;
use App\Models\ProductCount;
use App\Models\ProductDetail;
use App\Models\ProductGallery;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class Show extends Component
{
    use LivewireAlert;

    public array $data = [
        'title_fa' => null,
        'title_eng' => null,
        'categoryId' => null,
        'forId' => null,
        'description' => null,
        'meta_keywords' => null,
        'meta_description' => null,
        'amount' => null,
        'is_amount' => false,
        'count' => null,
        'eticket' => null,
        'commission' => null,
    ];

    public array $files = [];

    public $product;

    public function rules()
    {
        return [
            'data.title_fa' => 'required',
            'data.title_eng' => 'required',
            'data.categoryId' => 'required',
            'data.forId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title_fa.required' => 'پر کردن فیلد الزامیست',
            'data.title_eng.required' => 'پر کردن فیلد الزامیست',
            'data.categoryId.required' => 'پر کردن فیلد الزامیست',
            'data.forId.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function mount()
    {
        $this->data['title_fa'] = $this->product->title_fa;
        $this->data['title_eng'] = $this->product->title_eng;
        $this->data['categoryId'] = $this->product->category_id;
        $this->data['forId'] = $this->product->for_id;
        $this->data['description'] = $this->product->description;
        $this->data['meta_description'] = $this->product->meta_description;
        $this->data['meta_keywords'] = $this->product->meta_keywords;

        $this->data['is_amount'] = $this->data['amount'] != null ?? true;
        $this->data['amount'] = $this->product->fixed_amount;
        $this->data['count'] = $this->product?->productCountItem?->count;
        $this->data['eticket'] = $this->product->eticket;
        $this->data['commission'] = $this->product->fixed_commission;

        $this->files = $this->product->gallery->map(function ($image) {
            return [
                'image' => '/'.$image->url, // آدرس ذخیره‌شده در دیتابیس
                'is_old' => true, // مشخص‌کننده عکس‌های قدیمی
            ];
        })->toArray();

    }

    public function removeImage($key)
    {

        if (isset($this->files[$key])) {
            unset($this->files[$key]);
            $this->files = array_values($this->files);
        }
    }

    #[On('upload-product-image')]
    public function addImage($image)
    {
        array_push($this->files, $image);
    }

    public function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'product-gallery')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (! $fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid().'.'.pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (! is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath.$fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }

    public function update()
    {

        $this->validate();

        try {

            DB::beginTransaction();
            Product::whereId($this->product->id)->update([
                'title_fa' => $this->data['title_fa'],
                'title_eng' => $this->data['title_eng'],
                'slug' => Str::slug($this->data['title_eng'], '-'),
                'category_id' => $this->data['categoryId'],
                'for_id' => $this->data['forId'],
                'description' => $this->data['description'],
                'meta_keywords' => $this->data['meta_keywords'],
                'meta_description' => $this->data['meta_description'],
                'fixed_amount' => str_replace(',', '', $this->data['amount']),
                'fixed_commission' => str_replace(',', '', $this->data['commission']),
            ]);

            // if (! empty($this->data['amount'])) {
            //     $ProductDetail = ProductDetail::create([
            //         'product_id' => $this->product->id,
            //         'amount' => str_replace(',', '', $this->data['amount']),
            //         'eticket' => $this->data['eticket'],
            //     ]);
            //     ProductCount::create([
            //         'count' => $this->data['count'],
            //         'amount' => str_replace(',', '', $this->data['amount']),
            //         'product_detail_id' => $ProductDetail->id,
            //         'product_id' => $this->product->id,
            //     ]);
            // }

            // حذف تصاویر قدیمی از دیتابیس
            ProductGallery::where('product_id', $this->product->id)->delete();

            foreach ($this->files as $file) {
                if (isset($file['is_old']) && $file['is_old']) {
                    // تصاویر قدیمی را مجدد ذخیره کن
                    ProductGallery::create([
                        'product_id' => $this->product->id,
                        'url' => ltrim($file['image'], '/'),
                        'format' => 'image',
                    ]);
                } else {
                    // تصاویر جدید را ذخیره کن
                    $temporaryUrl = $file['image'];
                    $savedFilePath = $this->saveFileFromTemporaryUrl($temporaryUrl);

                    ProductGallery::create([
                        'product_id' => $this->product->id,
                        'url' => $savedFilePath,
                        'format' => 'image',
                    ]);
                }
            }

            DB::commit();

            Artisan::call('optimize:clear');

            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.show');
    }
}
