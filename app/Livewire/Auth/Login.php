<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use Illuminate\Http\Request;
use Carbon\Carbon;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Validate;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Validation\ValidationException;
use App\Jobs\AuthSMSJob;
class Login extends Component
{
    use LivewireAlert;
	use WithRateLimiting;

	#[Validate('required|numeric')]
    public $phone;
    public $exError, $page  = 'login';


    public function login(Request $req){

		try {

			$this->exError = '';
			$phone = faTOen($this->phone);

            $this->rateLimit(5);
			$this->validate();

			if(!preg_match("/^09[0-9]{9}$/", $phone)){
				return $this->exError = "فرمت شماره موبایل اشتباه است!";
			}

			$user = \App\Models\User::whereMobile($phone)->latest()->first();
			if(isset($user) && $user->count() > 0){
				if($user->status == 1){
					setUserLog($req, $phone, 'login');
					return $this->sendToken($phone, $user->fullname, $req);
				}else{
					return $this->exError = "اکانت شما غیرفعال شده است، با مدیریت سایت تماس بگیرید.";
				}
			}else{
				setUserLog($req, $phone, 'login-failed');
				return $this->exError = "کاربر مورد نظر یافت نشد، این بخش از سامانه برای کاربران شرکت تیدامد می باشد";
			}

        } catch (TooManyRequestsException $exception) {
			$this->exError = "تلاش های زیادی برای ورود به سیستم انجام شده. لطفا چند دقیقه دیگر دوباره اقدام به ورود کنید";
            throw ValidationException::withMessages([
                'message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید",
            ]);
        }

    }

	private function sendToken($phone, $username, $req){

		DB::beginTransaction();
        try{
			$token = rand ( 1000 , 9999 );

			\App\Models\Token::create([
				'phone' => $phone,
				'token' => $token,
				'ip' => $req->ip(),
			]);
			DB::commit();

            if (app()->environment('production')) {
                AuthSMSJob::dispatch($phone, $token, $username);
            }

			$this->exError = '';
			$this->page = 'verfication';

			return $this->alert('success', 'کدفعال سازی پیامک شد!', [
				'position' => 'top-start',
			]);



		}catch(\Exception $e){

			DB::rollback();
			$this->alert('error', 'خطا در سرور', [
				'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'خطایی در سرور بوجود آمده است، لطفاً چند دقیقه دیگر مجدد درخواست ثبت کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
		}

	}

	private function sendPayamak($token, $username, $phone){



		AuthSMSJob::dispatch($phone, $token, $username)->onQueue('auth-sms');
		$this->exError = '';
		$this->page = 'verfication';

		return $this->alert('success', 'کدفعال سازی پیامک شد!', [
			'position' => 'top-start',
		]);

	}

    public function render()
    {
        return view('livewire.auth.login');
    }
}
